{"version": 3, "targets": {"net7.0-windows7.0": {"ClickableTransparentOverlay/9.3.0": {"type": "package", "dependencies": {"ImGui.NET": "1.90.1.1", "SixLabors.ImageSharp": "3.1.2", "Vortice.D3DCompiler": "3.3.4", "Vortice.Direct3D11": "3.3.4"}, "compile": {"lib/net7.0/ClickableTransparentOverlay.dll": {}}, "runtime": {"lib/net7.0/ClickableTransparentOverlay.dll": {}}}, "DNNE/2.0.6": {"type": "package", "build": {"build/DNNE.props": {}, "build/DNNE.targets": {}}}, "Guna.UI2.WinForms/2.0.4.6": {"type": "package", "dependencies": {"System.Management": "7.0.0"}, "compile": {"lib/net7.0-windows7.0/Guna.UI2.dll": {}}, "runtime": {"lib/net7.0-windows7.0/Guna.UI2.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WindowsForms"]}, "ImGui.NET/1.90.1.1": {"type": "package", "dependencies": {"System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0", "System.Runtime.CompilerServices.Unsafe": "4.4.0"}, "compile": {"lib/net6.0/ImGui.NET.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/ImGui.NET.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/linux-x64/native/libcimgui.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/osx/native/libcimgui.dylib": {"assetType": "native", "rid": "osx"}, "runtimes/win-arm64/native/cimgui.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/cimgui.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/cimgui.dll": {"assetType": "native", "rid": "win-x86"}}}, "Microsoft.DotNet.ILCompiler/7.0.20": {"type": "package", "build": {"build/Microsoft.DotNet.ILCompiler.props": {}, "build/Microsoft.DotNet.ILCompiler.targets": {}}}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Analyzers.props": {}}}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "SharpGen.Runtime/2.1.2-beta": {"type": "package", "compile": {"lib/net7.0/SharpGen.Runtime.dll": {}}, "runtime": {"lib/net7.0/SharpGen.Runtime.dll": {}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "SharpGen.Runtime.COM/2.1.2-beta": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta"}, "compile": {"lib/net7.0/SharpGen.Runtime.COM.dll": {}}, "runtime": {"lib/net7.0/SharpGen.Runtime.COM.dll": {}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "SixLabors.ImageSharp/3.1.2": {"type": "package", "compile": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/SixLabors.ImageSharp.dll": {"related": ".xml"}}, "build": {"build/_._": {}}}, "System.Buffers/4.4.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.CodeDom/7.0.0": {"type": "package", "compile": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Management/7.0.0": {"type": "package", "dependencies": {"System.CodeDom": "7.0.0"}, "compile": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Numerics.Vectors/4.4.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}, "Vortice.D3DCompiler/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DirectX": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.D3DCompiler.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.D3DCompiler.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.Direct3D11/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DXGI": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.Direct3D11.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.Direct3D11.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.DirectX/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.DirectX.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.DirectX.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.DXGI/3.3.4": {"type": "package", "dependencies": {"SharpGen.Runtime": "2.1.2-beta", "SharpGen.Runtime.COM": "2.1.2-beta", "Vortice.DirectX": "3.3.4", "Vortice.Mathematics": "1.7.2"}, "compile": {"lib/net7.0/Vortice.DXGI.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.DXGI.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}, "buildMultiTargeting": {"buildMultiTargeting/_._": {}}}, "Vortice.Mathematics/1.7.2": {"type": "package", "compile": {"lib/net7.0/Vortice.Mathematics.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net7.0/Vortice.Mathematics.dll": {"related": ".pdb;.xml"}}}, "WinFormsComInterop/0.5.0": {"type": "package", "compile": {"lib/net7.0/WinFormsComInterop.dll": {}}, "runtime": {"lib/net7.0/WinFormsComInterop.dll": {}}}}}, "libraries": {"ClickableTransparentOverlay/9.3.0": {"sha512": "DxhW1jh50MRyZZtSKmKUjxg8ik0rvCxnUMc22UjOHLlVQtFaxS8QOIYLjEed4X+CCsl0VXJybNlvBxJisZX83Q==", "type": "package", "path": "clickabletransparentoverlay/9.3.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "clickabletransparentoverlay.9.3.0.nupkg.sha512", "clickabletransparentoverlay.nuspec", "lib/net7.0/ClickableTransparentOverlay.dll"]}, "DNNE/2.0.6": {"sha512": "C9f/e0nlN1iIvIhmQFcr84KDuEQgkxGpAX4Cde+4ZaGegiSEGRV9M4vGDhfRiw7b8DW7/zObcFOkORcEAgoMng==", "type": "package", "path": "dnne/2.0.6", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/dnne-analyzers.dll", "build/DNNE.Analyzers.targets", "build/DNNE.props", "build/DNNE.targets", "build/net472/DNNE.BuildTasks.dll", "build/net472/DNNE.BuildTasks.pdb", "build/net472/Microsoft.Build.Framework.dll", "build/net472/Microsoft.Build.Utilities.Core.dll", "build/net472/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "build/net472/System.Collections.Immutable.dll", "build/netstandard2.1/DNNE.BuildTasks.deps.json", "build/netstandard2.1/DNNE.BuildTasks.dll", "build/netstandard2.1/DNNE.BuildTasks.pdb", "dnne.2.0.6.nupkg.sha512", "dnne.nuspec", "tools/dnne-gen.dll", "tools/dnne-gen.runtimeconfig.json", "tools/platform/dnne.h", "tools/platform/platform.c", "tools/platform/platform_v4.cpp"]}, "Guna.UI2.WinForms/2.0.4.6": {"sha512": "NYzSJp7e47Cz3NgGCHvzadash8ycmxxUnpLg4Rr/MSYG9WDw0HOoTI4SkbDW+HbACrf6tsWYCuzWkgvjuM/O6w==", "type": "package", "path": "guna.ui2.winforms/2.0.4.6", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "guna.ui2.winforms.2.0.4.6.nupkg.sha512", "guna.ui2.winforms.nuspec", "icon.png", "lib/net40/Guna.UI2.dll", "lib/net45/Guna.UI2.dll", "lib/net461/Guna.UI2.dll", "lib/net472/Guna.UI2.dll", "lib/net48/Guna.UI2.dll", "lib/net6.0-windows7.0/Guna.UI2.dll", "lib/net7.0-windows7.0/Guna.UI2.dll", "lib/netcoreapp3.1/Guna.UI2.dll"]}, "ImGui.NET/1.90.1.1": {"sha512": "/VNgkRywtXV72cpxtBj09nZX9Yx+xG3NxiwEEXPVcK+pHKHA31cPJKy425tDGj68tgRbCS+C+DG8puBj3iJ4zA==", "type": "package", "path": "imgui.net/1.90.1.1", "files": [".nupkg.metadata", ".signature.p7s", "build/net40/ImGui.NET.targets", "imgui.net.1.90.1.1.nupkg.sha512", "imgui.net.nuspec", "lib/net6.0/ImGui.NET.dll", "lib/net6.0/ImGui.NET.xml", "lib/netstandard2.0/ImGui.NET.dll", "lib/netstandard2.0/ImGui.NET.xml", "runtimes/linux-x64/native/libcimgui.so", "runtimes/osx/native/libcimgui.dylib", "runtimes/win-arm64/native/cimgui.dll", "runtimes/win-x64/native/cimgui.dll", "runtimes/win-x86/native/cimgui.dll"]}, "Microsoft.DotNet.ILCompiler/7.0.20": {"sha512": "9gGpu74pc/FBQuHqTb0pnvTftfSpQIkOfic7z9czAPTlCxEvY8rsEsYcfIX7ExvcIEr35Rf5cpxqCH09gApW3Q==", "type": "package", "path": "microsoft.dotnet.ilcompiler/7.0.20", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "build/BuildFrameworkNativeObjects.proj", "build/Microsoft.DotNet.ILCompiler.SingleEntry.targets", "build/Microsoft.DotNet.ILCompiler.props", "build/Microsoft.DotNet.ILCompiler.targets", "build/Microsoft.NETCore.Native.Publish.targets", "build/Microsoft.NETCore.Native.Unix.targets", "build/Microsoft.NETCore.Native.Windows.targets", "build/Microsoft.NETCore.Native.targets", "build/NativeAOT.natvis", "build/WindowsAPIs.txt", "build/findvcvarsall.bat", "microsoft.dotnet.ilcompiler.7.0.20.nupkg.sha512", "microsoft.dotnet.ilcompiler.nuspec", "runtime.json", "tools/netstandard/ILCompiler.Build.Tasks.deps.json", "tools/netstandard/ILCompiler.Build.Tasks.dll", "tools/netstandard/ILCompiler.Build.Tasks.pdb", "tools/netstandard/System.Collections.Immutable.dll", "tools/netstandard/System.Reflection.Metadata.dll"]}, "Microsoft.NET.ILLink.Analyzers/7.0.100-1.23211.1": {"sha512": "0GvbEgDGcUQA9KuWcQU1WwYHXt1tBzNr1Nls/M57rM7NA/AndFwCaCEoJpJkmxRY7xLlPDBnmGp8h5+FNqUngg==", "type": "package", "path": "microsoft.net.illink.analyzers/7.0.100-1.23211.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "microsoft.net.illink.analyzers.7.0.100-1.23211.1.nupkg.sha512", "microsoft.net.illink.analyzers.nuspec"]}, "Microsoft.NET.ILLink.Tasks/7.0.100-1.23211.1": {"sha512": "tvG8XZYLjT0o3WicCyKBZysVWo1jC9HdCFmNRmddx3WbAz0UCsd0qKZqpiEo99VLA8Re+FzWK51OcRldQPbt2Q==", "type": "package", "path": "microsoft.net.illink.tasks/7.0.100-1.23211.1", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "Sdk/Sdk.props", "build/6.0_suppressions.xml", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.7.0.100-1.23211.1.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net7.0/ILLink.Tasks.deps.json", "tools/net7.0/ILLink.Tasks.dll", "tools/net7.0/Mono.Cecil.Pdb.dll", "tools/net7.0/Mono.Cecil.dll", "tools/net7.0/illink.deps.json", "tools/net7.0/illink.dll", "tools/net7.0/illink.runtimeconfig.json"]}, "SharpGen.Runtime/2.1.2-beta": {"sha512": "nqZAjfEG1jX1ivvdZLsi6Pkt0DiOJyuOgRgldNFsmjXFPhxUbXQibofLSwuDZidL2kkmtTF8qLoRIeqeVdXgYw==", "type": "package", "path": "sharpgen.runtime/2.1.2-beta", "files": [".nupkg.metadata", ".signature.p7s", "build/Mapping.xml", "build/SharpGen.Runtime.props", "buildMultiTargeting/SharpGen.Runtime.props", "lib/net461/SharpGen.Runtime.dll", "lib/net471/SharpGen.Runtime.dll", "lib/net7.0/SharpGen.Runtime.dll", "lib/net8.0/SharpGen.Runtime.dll", "lib/netcoreapp3.1/SharpGen.Runtime.dll", "lib/netstandard2.0/SharpGen.Runtime.dll", "lib/netstandard2.1/SharpGen.Runtime.dll", "sharpgen.runtime.2.1.2-beta.nupkg.sha512", "sharpgen.runtime.nuspec"]}, "SharpGen.Runtime.COM/2.1.2-beta": {"sha512": "HBCrb6HfnUWx9v5/GjJeBr5DuodZLnHlFQQYXPrQs1Hbe1c6Wd0uCXf+SJp4hW8fQNxjXEu0FgiyHGlA/SRzRw==", "type": "package", "path": "sharpgen.runtime.com/2.1.2-beta", "files": [".nupkg.metadata", ".signature.p7s", "build/SharpGen.Runtime.COM.BindMapping.xml", "build/SharpGen.Runtime.COM.props", "buildMultiTargeting/SharpGen.Runtime.COM.props", "lib/net461/SharpGen.Runtime.COM.dll", "lib/net471/SharpGen.Runtime.COM.dll", "lib/net7.0/SharpGen.Runtime.COM.dll", "lib/net8.0/SharpGen.Runtime.COM.dll", "lib/netcoreapp3.1/SharpGen.Runtime.COM.dll", "lib/netstandard2.0/SharpGen.Runtime.COM.dll", "lib/netstandard2.1/SharpGen.Runtime.COM.dll", "sharpgen.runtime.com.2.1.2-beta.nupkg.sha512", "sharpgen.runtime.com.nuspec"]}, "SixLabors.ImageSharp/3.1.2": {"sha512": "PYdR6GUI+gW6LBaAQKTik0Ai8oLpFAz3a/KrVusxoTg3kf7F3cuIqKMhJGsuQcmDHCF+iD81Pyn4cexyHrb1ZA==", "type": "package", "path": "sixlabors.imagesharp/3.1.2", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE", "build/SixLabors.ImageSharp.props", "lib/net6.0/SixLabors.ImageSharp.dll", "lib/net6.0/SixLabors.ImageSharp.xml", "sixlabors.imagesharp.128.png", "sixlabors.imagesharp.3.1.2.nupkg.sha512", "sixlabors.imagesharp.nuspec"]}, "System.Buffers/4.4.0": {"sha512": "AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "type": "package", "path": "system.buffers/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "system.buffers.4.4.0.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.CodeDom/7.0.0": {"sha512": "GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "type": "package", "path": "system.codedom/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net6.0/System.CodeDom.dll", "lib/net6.0/System.CodeDom.xml", "lib/net7.0/System.CodeDom.dll", "lib/net7.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.7.0.0.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Management/7.0.0": {"sha512": "A4jed4QUviDOm7fJNKAJObEAEkEUXmkGL/w0iyCYTzrl1rezTj8LGFHfsVst4Vb9JwFcTpboiDrvdST48avBpw==", "type": "package", "path": "system.management/7.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net6.0/System.Management.dll", "lib/net6.0/System.Management.xml", "lib/net7.0/System.Management.dll", "lib/net7.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net6.0/System.Management.dll", "runtimes/win/lib/net6.0/System.Management.xml", "runtimes/win/lib/net7.0/System.Management.dll", "runtimes/win/lib/net7.0/System.Management.xml", "system.management.7.0.0.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Numerics.Vectors/4.4.0": {"sha512": "UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "type": "package", "path": "system.numerics.vectors/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.4.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/4.4.0": {"sha512": "9dLLuBxr5GNmOfl2jSMcsHuteEg32BEfUotmmUkmZjpR3RpVHE8YQwt0ow3p6prwA1ME8WqDVZqrr8z6H8G+Kw==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.4.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.4.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Vortice.D3DCompiler/3.3.4": {"sha512": "G0yAN1us091ulIzPfMVJeJf8JX3Tu/Wu0l4zip4HPLfVUWWxsBJF0eaEdwSPrUyz97LEG6zjHruL4sZoNRBHcA==", "type": "package", "path": "vortice.d3dcompiler/3.3.4", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.D3DCompiler.BindMapping.xml", "build/Vortice.D3DCompiler.props", "buildMultiTargeting/Vortice.D3DCompiler.props", "lib/net7.0/Vortice.D3DCompiler.dll", "lib/net7.0/Vortice.D3DCompiler.pdb", "lib/net7.0/Vortice.D3DCompiler.xml", "lib/net8.0/Vortice.D3DCompiler.dll", "lib/net8.0/Vortice.D3DCompiler.pdb", "lib/net8.0/Vortice.D3DCompiler.xml", "vortice.d3dcompiler.3.3.4.nupkg.sha512", "vortice.d3dcompiler.nuspec"]}, "Vortice.Direct3D11/3.3.4": {"sha512": "ma8ysRo7WBmJwt+nNpFi0icHKIgNud6Nb0rz6XPN4V/X93nkVZiN3t96cqdy0kC0QtzYlpFccQ2d/JPrvHt6og==", "type": "package", "path": "vortice.direct3d11/3.3.4", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.Direct3D11.BindMapping.xml", "build/Vortice.Direct3D11.props", "buildMultiTargeting/Vortice.Direct3D11.props", "lib/net7.0/Vortice.Direct3D11.dll", "lib/net7.0/Vortice.Direct3D11.pdb", "lib/net7.0/Vortice.Direct3D11.xml", "lib/net8.0/Vortice.Direct3D11.dll", "lib/net8.0/Vortice.Direct3D11.pdb", "lib/net8.0/Vortice.Direct3D11.xml", "vortice.direct3d11.3.3.4.nupkg.sha512", "vortice.direct3d11.nuspec"]}, "Vortice.DirectX/3.3.4": {"sha512": "HWvIGYz8XtWZpJW8xGYq5gex/HUPf5u3kDY7w/5WLCXzuR+hqLB7p6tInYePnuVLvfuI/rKMMhlzmnceoP3tIw==", "type": "package", "path": "vortice.directx/3.3.4", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.DirectX.BindMapping.xml", "build/Vortice.DirectX.props", "buildMultiTargeting/Vortice.DirectX.props", "lib/net7.0/Vortice.DirectX.dll", "lib/net7.0/Vortice.DirectX.pdb", "lib/net7.0/Vortice.DirectX.xml", "lib/net8.0-windows10.0.19041/Vortice.DirectX.dll", "lib/net8.0-windows10.0.19041/Vortice.DirectX.pdb", "lib/net8.0-windows10.0.19041/Vortice.DirectX.xml", "lib/net8.0/Vortice.DirectX.dll", "lib/net8.0/Vortice.DirectX.pdb", "lib/net8.0/Vortice.DirectX.xml", "vortice.directx.3.3.4.nupkg.sha512", "vortice.directx.nuspec"]}, "Vortice.DXGI/3.3.4": {"sha512": "toya4AmKpcVJyaZzDJnrZlYsVSQxHTTrwWYee3G4Dzthww5HTgtGluN5ciQKl0KOChbR9xjgSK8+flkw6RNdTg==", "type": "package", "path": "vortice.dxgi/3.3.4", "files": [".nupkg.metadata", ".signature.p7s", "build/Vortice.DXGI.BindMapping.xml", "build/Vortice.DXGI.props", "buildMultiTargeting/Vortice.DXGI.props", "lib/net7.0/Vortice.DXGI.dll", "lib/net7.0/Vortice.DXGI.pdb", "lib/net7.0/Vortice.DXGI.xml", "lib/net8.0-windows10.0.19041/Vortice.DXGI.dll", "lib/net8.0-windows10.0.19041/Vortice.DXGI.pdb", "lib/net8.0-windows10.0.19041/Vortice.DXGI.xml", "lib/net8.0/Vortice.DXGI.dll", "lib/net8.0/Vortice.DXGI.pdb", "lib/net8.0/Vortice.DXGI.xml", "vortice.dxgi.3.3.4.nupkg.sha512", "vortice.dxgi.nuspec"]}, "Vortice.Mathematics/1.7.2": {"sha512": "WUUzB3MowHdOqNfSMAms5Ovra+SKgLC4ttH1w75ra3srDGctebSg+aywm7hLdvDn592ydgdbOJN11JKsyAcX+A==", "type": "package", "path": "vortice.mathematics/1.7.2", "files": [".nupkg.metadata", ".signature.p7s", "lib/net7.0/Vortice.Mathematics.dll", "lib/net7.0/Vortice.Mathematics.pdb", "lib/net7.0/Vortice.Mathematics.xml", "lib/net8.0-windows10.0.19041/Vortice.Mathematics.dll", "lib/net8.0-windows10.0.19041/Vortice.Mathematics.pdb", "lib/net8.0-windows10.0.19041/Vortice.Mathematics.xml", "lib/net8.0/Vortice.Mathematics.dll", "lib/net8.0/Vortice.Mathematics.pdb", "lib/net8.0/Vortice.Mathematics.xml", "vortice.mathematics.1.7.2.nupkg.sha512", "vortice.mathematics.nuspec"]}, "WinFormsComInterop/0.5.0": {"sha512": "afCh9mbBjXfkTansOWtmCDjdtzudIcFLxRUqrgL3CfaariduFMhAleKyp/QKWP+xSfSzE8FwQWNLqH+bUE0csw==", "type": "package", "path": "winformscominterop/0.5.0", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net6.0/WinFormsComInterop.dll", "lib/net7.0/WinFormsComInterop.dll", "lib/net8.0/WinFormsComInterop.dll", "winformscominterop.0.5.0.nupkg.sha512", "winformscominterop.nuspec"]}}, "projectFileDependencyGroups": {"net7.0-windows7.0": ["ClickableTransparentOverlay >= 9.3.0", "DNNE >= 2.0.6", "Guna.UI2.WinForms >= 2.0.4.6", "Microsoft.DotNet.ILCompiler >= 7.0.20", "Microsoft.NET.ILLink.Analyzers >= 7.0.100-1.23211.1", "Microsoft.NET.ILLink.Tasks >= 7.0.100-1.23211.1", "WinFormsComInterop >= 0.5.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\INTERNAL SAFE\\AotForms\\AotForms.csproj", "projectName": "AotForms", "projectPath": "C:\\Users\\<USER>\\Desktop\\INTERNAL SAFE\\AotForms\\AotForms.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\INTERNAL SAFE\\AotForms\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"ClickableTransparentOverlay": {"target": "Package", "version": "[9.3.0, )"}, "DNNE": {"target": "Package", "version": "[2.0.6, )"}, "Guna.UI2.WinForms": {"target": "Package", "version": "[2.0.4.6, )"}, "Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[7.0.20, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "WinFormsComInterop": {"target": "Package", "version": "[0.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "runtime.win-x64.Microsoft.DotNet.ILCompiler", "version": "[7.0.20, 7.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}