using System;
using System.Diagnostics;
using System.Runtime.InteropServices;
using System.Windows.Forms;

namespace AotForms
{
    internal static class Program
    {
        [UnmanagedCallersOnly(EntryPoint = "Load")]
        public static void Load(nint pVM)
        {
            try
            {
                // Initialize performance configuration
                PerformanceConfig.ValidateSettings();
                PerformanceConfig.OptimizeForSystem();

                // Apply performance optimizations early
                PerformanceOptimizer.OptimizeForGameLoop();

                // Initialize internal memory with provided VM pointer
                InternalMemory.Initialize(pVM);
                ErrorHandler.LogInfo("InternalMemory initialized successfully");
                ErrorHandler.LogInfo(PerformanceConfig.GetConfigurationSummary());

                // Set high DPI mode and enable visual styles
                Application.SetHighDpiMode(HighDpiMode.PerMonitorV2);
                Application.EnableVisualStyles();

                // Set up global exception handlers
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                // Get current process
                var process = Process.GetCurrentProcess();

                // Run Form2 first (login)
                Application.Run(new Form2(process.MainWindowHandle));

                // After Form2 is closed, run Form1 (main application)
                Application.Run(new Form1(IntPtr.Zero));
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Critical error in Load method", ex);
                MessageBox.Show($"Critical error: {ex.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Cleanup resources
                PerformanceOptimizer.CleanupResources();
            }
        }

        private static void Application_ThreadException(object sender, ThreadExceptionEventArgs e)
        {
            ErrorHandler.LogError("Unhandled thread exception", e.Exception);
            MessageBox.Show($"An error occurred: {e.Exception.Message}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                ErrorHandler.LogError("Unhandled domain exception", ex);
                MessageBox.Show($"Critical error: {ex.Message}", "Fatal Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // Descomente se precisar de um ponto de entrada aut�nomo para testes
        //public static void Main(string[] args) {
        //    Application.SetHighDpiMode(HighDpiMode.PerMonitorV2);
        //    Application.EnableVisualStyles();
        //    Application.Run(new Main(IntPtr.Zero)); // Executa o Form1
        //}
    }
}
