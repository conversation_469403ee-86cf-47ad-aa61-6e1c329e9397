﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Numerics;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace AotForms
{
    internal static class Data
    {
        private static CancellationTokenSource _cancellationTokenSource = new();
        private static bool _isRunning = false;
        private static readonly object _lockObject = new object();
        private static int _errorCount = 0;
        private static DateTime _lastErrorReset = DateTime.Now;

        internal static async Task StartAsync()
        {
            lock (_lockObject)
            {
                if (_isRunning) return;
                _isRunning = true;
            }

            _cancellationTokenSource = new CancellationTokenSource();
            await Task.Run(() => WorkAsync(_cancellationTokenSource.Token), _cancellationTokenSource.Token);
        }

        internal static void Stop()
        {
            lock (_lockObject)
            {
                if (!_isRunning) return;
                _isRunning = false;
            }

            _cancellationTokenSource?.Cancel();
        }

        private static async Task WorkAsync(CancellationToken cancellationToken)
        {
            var targetFrameTime = PerformanceConfig.GetTargetFrameTime(PerformanceConfig.DataCollectionTargetFPS);
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    stopwatch.Restart();

                    try
                    {
                        await ProcessGameData(cancellationToken);

                        // Reset error count every minute
                        if (DateTime.Now - _lastErrorReset > TimeSpan.FromMinutes(1))
                        {
                            _errorCount = 0;
                            _lastErrorReset = DateTime.Now;
                        }
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        _errorCount++;
                        ErrorHandler.LogError("Data processing error", ex);

                        // If too many errors, increase delay
                        var errorDelay = Math.Min(_errorCount * PerformanceConfig.ErrorDelayMultiplier, PerformanceConfig.MaxErrorDelay);
                        await Task.Delay(errorDelay, cancellationToken);

                        // Reset cache on persistent errors
                        if (_errorCount > PerformanceConfig.MaxErrorsBeforeReset)
                        {
                            ErrorHandler.LogWarning("Too many errors, resetting cache");
                            ResetCache();
                        }
                    }

                    // Frame rate limiting
                    var elapsed = stopwatch.ElapsedMilliseconds;
                    if (elapsed < targetFrameTime)
                    {
                        await Task.Delay((int)(targetFrameTime - elapsed), cancellationToken);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
            finally
            {
                lock (_lockObject)
                {
                    _isRunning = false;
                }
            }
        }

        private static async Task ProcessGameData(CancellationToken cancellationToken)
        {
            // Set matrix state
            Core.HaveMatrix = Config.FixEsp;

            // Read base game facade
            if (!InternalMemory.Read<uint>(Offsets.Il2Cpp + Offsets.InitBase, out var baseGameFacade) || baseGameFacade == 0)
            {
                ResetCache();
                return;
            }

            // Read game facade
            if (!InternalMemory.Read<uint>(baseGameFacade, out var gameFacade) || gameFacade == 0)
            {
                ResetCache();
                return;
            }

            // Read static game facade
            if (!InternalMemory.Read<uint>(gameFacade + Offsets.StaticClass, out var staticGameFacade) || staticGameFacade == 0)
            {
                ResetCache();
                return;
            }

            // Read current game
            if (!InternalMemory.Read<uint>(staticGameFacade, out var currentGame) || currentGame == 0)
            {
                ResetCache();
                return;
            }

            // Read current match
            if (!InternalMemory.Read<uint>(currentGame + Offsets.CurrentMatch, out var currentMatch) || currentMatch == 0)
            {
                ResetCache();
                return;
            }

            // Read local player
            if (!InternalMemory.Read<uint>(currentMatch + Offsets.LocalPlayer, out var localPlayer) || localPlayer == 0)
            {
                return;
            }

            // Read main camera transform
            if (!InternalMemory.Read<uint>(localPlayer + Offsets.MainCameraTransform, out var mainTransform) || mainTransform == 0)
            {
                return;
            }

            // Get camera position
            if (Transform.GetPosition(mainTransform, out var mainPos))
            {
                Core.LocalMainCamera = mainPos;
            }

            // Read follow camera
            if (!InternalMemory.Read<uint>(localPlayer + Offsets.FollowCamera, out var followCamera) || followCamera == 0)
            {
                return;
            }

            // Read camera
            if (!InternalMemory.Read<uint>(followCamera + Offsets.Camera, out var camera) || camera == 0)
            {
                return;
            }

            // Read camera base
            if (!InternalMemory.Read<uint>(camera + 0x8, out var cameraBase) || cameraBase == 0)
            {
                return;
            }

            Core.HaveMatrix = true;

            // Read view matrix
            if (!InternalMemory.Read<Matrix4x4>(cameraBase + Offsets.ViewMatrix, out var viewMatrix))
            {
                return;
            }
            Core.CameraMatrix = viewMatrix;

            // Read entity dictionary
            if (!InternalMemory.Read<uint>(currentGame + Offsets.DictionaryEntities, out var entityDictionary) || entityDictionary == 0)
            {
                ResetCache();
                return;
            }

            // Read entities
            if (!InternalMemory.Read<uint>(entityDictionary + 0x14, out var entities) || entities == 0)
            {
                ResetCache();
                return;
            }

            entities = entities + 0x10;

            // Read entities count
            if (!InternalMemory.Read<uint>(entityDictionary + 0x18, out var entitiesCount) || entitiesCount < 1)
                return;

            // Process entities with cancellation support
            await ProcessEntities(entities, entitiesCount, localPlayer, mainPos, cancellationToken);
        }

        private static async Task ProcessEntities(uint entities, uint entitiesCount, uint localPlayer, Vector3 mainPos, CancellationToken cancellationToken)
        {
            var batchSize = PerformanceConfig.EntityBatchSize;
            var processedCount = 0;

            for (int i = 0; i < entitiesCount && !cancellationToken.IsCancellationRequested; i++)
            {
                try
                {
                    if (!InternalMemory.Read<uint>((ulong)(i * 0x4 + entities), out var entity) || entity == 0 || entity == localPlayer)
                        continue;

                    await ProcessSingleEntity(entity, mainPos, cancellationToken);

                    processedCount++;

                    // Yield control every batch to prevent blocking
                    if (processedCount % batchSize == 0)
                    {
                        await Task.Yield();
                    }
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError($"Entity {i} processing error", ex);
                    continue;
                }
            }
        }

        private static async Task ProcessSingleEntity(uint entity, Vector3 mainPos, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            if (!Core.Entities.TryGetValue(entity, out var player))
            {
                // Create new entity
                Core.Entities[entity] = new Entity
                {
                    IsTeam = Bool3.Unknown,
                    IsKnown = false,
                    IsDead = false,
                    Health = 0,
                    IsKnocked = false,
                    Head = Vector3.Zero,
                    LeftWrist = Vector3.Zero,
                    Spine = Vector3.Zero,
                    Root = Vector3.Zero,
                    Hip = Vector3.Zero,
                    RightCalf = Vector3.Zero,
                    LeftCalf = Vector3.Zero,
                    RightFoot = Vector3.Zero,
                    LeftFoot = Vector3.Zero,
                    RightWrist = Vector3.Zero,
                    LeftHand = Vector3.Zero,
                    RightShoulder = Vector3.Zero,
                    LeftShoulder = Vector3.Zero,
                    RightWristJoint = Vector3.Zero,
                    LeftWristJoint = Vector3.Zero,
                    RightElbow = Vector3.Zero,
                    LeftElbow = Vector3.Zero,
                    Name = ""
                };
                return;
            }

            // Skip team members
            if (player.IsTeam == Bool3.True) return;

            // Check team status if unknown
            if (player.IsTeam == Bool3.Unknown)
            {
                if (InternalMemory.Read<uint>(entity + Offsets.AvatarManager, out var avatarManager) && avatarManager != 0)
                {
                    if (InternalMemory.Read<uint>(avatarManager + Offsets.Avatar, out var avatar) && avatar != 0)
                    {
                        if (InternalMemory.Read<bool>(avatar + Offsets.Avatar_IsVisible, out var isVisible) && isVisible)
                        {
                            if (InternalMemory.Read<uint>(avatar + Offsets.Avatar_Data, out var avatarData) && avatarData != 0)
                            {
                                if (InternalMemory.Read<bool>(avatarData + Offsets.Avatar_Data_IsTeam, out var isTeam))
                                {
                                    player.IsTeam = isTeam ? Bool3.True : Bool3.False;
                                    if (!isTeam) player.IsKnown = true;
                                }
                            }
                        }
                    }
                }
            }

            if (!player.IsKnown) return;

            // Check knocked status
            if (Config.IgnoreKnocked)
            {
                if (InternalMemory.Read<uint>(entity + Offsets.Player_ShadowBase, out var shadowBase) && shadowBase != 0)
                {
                    if (InternalMemory.Read<int>(shadowBase + Offsets.XPose, out var xpose))
                    {
                        player.IsKnocked = xpose == 8;
                    }
                }
            }

            // Check death status
            if (InternalMemory.Read<bool>(entity + Offsets.Player_IsDead, out var isDead))
            {
                player.IsDead = isDead;
            }

            // Read player name and health data
            await ReadPlayerData(entity, player, cancellationToken);

            // Read bone positions
            await ReadBonePositions(entity, player, mainPos, cancellationToken);
        }

        private static async Task ReadPlayerData(uint entity, Entity player, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            // Read name if ESP name is enabled
            if (Config.ESPName)
            {
                if (InternalMemory.Read<uint>(entity + Offsets.Player_Name, out var nameAddr) && nameAddr != 0)
                {
                    if (InternalMemory.Read<int>(nameAddr + 0x8, out var nameLen) && nameLen > 0)
                    {
                        var name = InternalMemory.ReadString(nameAddr + 0xC, nameLen);
                        if (!string.IsNullOrEmpty(name))
                        {
                            player.Name = name;
                        }
                    }
                }
            }

            // Read health data (combined for both ESP name and health)
            if (Config.ESPName || Config.ESPHealth)
            {
                if (InternalMemory.Read<uint>(entity + Offsets.Player_Data, out var dataPool) && dataPool != 0)
                {
                    if (InternalMemory.Read<uint>(dataPool + 0x8, out var poolObj) && poolObj != 0)
                    {
                        if (InternalMemory.Read<uint>(poolObj + 0x10, out var pool) && pool != 0)
                        {
                            // Try different health offsets
                            if (InternalMemory.Read<short>(pool + 0xC, out var health1) && health1 > 0)
                            {
                                player.Health = health1;
                            }
                            else if (InternalMemory.Read<short>(pool + 0x10, out var health2) && health2 > 0)
                            {
                                player.Health = health2;
                            }
                        }
                    }
                }
            }
        }

        private static async Task ReadBonePositions(uint entity, Entity player, Vector3 mainPos, CancellationToken cancellationToken)
        {
            cancellationToken.ThrowIfCancellationRequested();

            var boneOffsets = new[]
            {
                Bones.Head, Bones.LeftWrist, Bones.Spine, Bones.Hip, Bones.Root,
                Bones.RightCalf, Bones.LeftCalf, Bones.RightFoot, Bones.LeftFoot,
                Bones.RightWrist, Bones.LeftHand, Bones.LeftShoulder, Bones.RightShoulder,
                Bones.RightWristJoint, Bones.LeftWristJoint, Bones.LeftElbow, Bones.RightElbow
            };

            foreach (var offset in boneOffsets)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (InternalMemory.Read<uint>(entity + (uint)offset, out var bone) && bone != 0)
                {
                    if (Transform.GetNodePosition(bone, out var boneTransform))
                    {
                        switch (offset)
                        {
                            case Bones.Head:
                                player.Head = boneTransform;
                                player.Distance = Vector3.Distance(mainPos, boneTransform);
                                break;
                            case Bones.LeftWrist:
                                player.LeftWrist = boneTransform;
                                break;
                            case Bones.Spine:
                                player.Spine = boneTransform;
                                break;
                            case Bones.Hip:
                                player.Hip = boneTransform;
                                break;
                            case Bones.Root:
                                player.Root = boneTransform;
                                break;
                            case Bones.RightCalf:
                                player.RightCalf = boneTransform;
                                break;
                            case Bones.LeftCalf:
                                player.LeftCalf = boneTransform;
                                break;
                            case Bones.RightFoot:
                                player.RightFoot = boneTransform;
                                break;
                            case Bones.LeftFoot:
                                player.LeftFoot = boneTransform;
                                break;
                            case Bones.RightWrist:
                                player.RightWrist = boneTransform;
                                break;
                            case Bones.LeftHand:
                                player.LeftHand = boneTransform;
                                break;
                            case Bones.LeftShoulder:
                                player.LeftShoulder = boneTransform;
                                break;
                            case Bones.RightShoulder:
                                player.RightShoulder = boneTransform;
                                break;
                            case Bones.RightWristJoint:
                                player.RightWristJoint = boneTransform;
                                break;
                            case Bones.LeftWristJoint:
                                player.LeftWristJoint = boneTransform;
                                break;
                            case Bones.RightElbow:
                                player.RightElbow = boneTransform;
                                break;
                            case Bones.LeftElbow:
                                player.LeftElbow = boneTransform;
                                break;
                        }
                    }
                }
            }
        }

        // Legacy method for backward compatibility
        internal static void Work()
        {
            _ = StartAsync();
        }

        private static void ResetCache()
        {
            try
            {
                Core.Entities?.Clear();
                InternalMemory.Cache?.Clear();

                // Recreate collections if they're null
                Core.Entities ??= new();
                InternalMemory.Cache ??= new();
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Cache reset error", ex);
            }
        }
    }
}
