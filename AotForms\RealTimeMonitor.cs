using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace AotForms
{
    /// <summary>
    /// Real-time performance monitoring and adaptive optimization
    /// </summary>
    internal static class RealTimeMonitor
    {
        private static Timer _monitorTimer;
        private static readonly ConcurrentDictionary<string, PerformanceMetrics> _metrics = new();
        private static volatile bool _isMonitoring = false;
        private static readonly object _lockObject = new object();

        public class PerformanceMetrics
        {
            public double AverageFPS { get; set; }
            public long AverageMemoryUsage { get; set; }
            public int ErrorCount { get; set; }
            public DateTime LastUpdate { get; set; }
            public double CPUUsage { get; set; }
            public int ActiveThreads { get; set; }
            public int CacheHitRate { get; set; }
        }

        /// <summary>
        /// Start real-time monitoring
        /// </summary>
        public static void StartMonitoring()
        {
            lock (_lockObject)
            {
                if (_isMonitoring) return;

                try
                {
                    _monitorTimer = new Timer(CollectMetrics, null, 
                        TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
                    
                    _isMonitoring = true;
                    ErrorHandler.LogInfo("Real-time monitoring started");
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError("Failed to start real-time monitoring", ex);
                }
            }
        }

        /// <summary>
        /// Stop monitoring
        /// </summary>
        public static void StopMonitoring()
        {
            lock (_lockObject)
            {
                if (!_isMonitoring) return;

                try
                {
                    _monitorTimer?.Dispose();
                    _monitorTimer = null;
                    _isMonitoring = false;
                    
                    ErrorHandler.LogInfo("Real-time monitoring stopped");
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError("Error stopping monitoring", ex);
                }
            }
        }

        /// <summary>
        /// Collect performance metrics
        /// </summary>
        private static void CollectMetrics(object state)
        {
            try
            {
                var metrics = new PerformanceMetrics
                {
                    AverageFPS = FrameRateLimiter.GetCurrentFps(),
                    AverageMemoryUsage = GC.GetTotalMemory(false),
                    LastUpdate = DateTime.Now,
                    ActiveThreads = Process.GetCurrentProcess().Threads.Count,
                    CacheHitRate = CalculateCacheHitRate()
                };

                // Store metrics with timestamp
                var key = DateTime.Now.ToString("HH:mm:ss");
                _metrics.TryAdd(key, metrics);

                // Keep only last 60 seconds of data
                if (_metrics.Count > 60)
                {
                    var oldestKey = DateTime.Now.AddSeconds(-60).ToString("HH:mm:ss");
                    _metrics.TryRemove(oldestKey, out _);
                }

                // Adaptive optimization based on metrics
                PerformAdaptiveOptimization(metrics);

                // Log critical metrics every 10 seconds
                if (DateTime.Now.Second % 10 == 0)
                {
                    LogCurrentMetrics(metrics);
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Metrics collection error", ex);
            }
        }

        /// <summary>
        /// Calculate cache hit rate
        /// </summary>
        private static int CalculateCacheHitRate()
        {
            try
            {
                var cacheSize = InternalMemory.GetCacheSize();
                var maxCacheSize = PerformanceConfig.MaxCacheSize;
                
                return maxCacheSize > 0 ? (int)((double)cacheSize / maxCacheSize * 100) : 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Perform adaptive optimization based on current metrics
        /// </summary>
        private static void PerformAdaptiveOptimization(PerformanceMetrics metrics)
        {
            try
            {
                // Adaptive FPS adjustment based on performance
                if (metrics.AverageFPS < PerformanceConfig.AimbotTargetFPS * 0.8) // 80% of target
                {
                    // Performance is low, reduce targets
                    PerformanceConfig.AimbotTargetFPS = Math.Max(30, PerformanceConfig.AimbotTargetFPS - 5);
                    PerformanceConfig.MaxEntitiesPerFrame = Math.Max(10, PerformanceConfig.MaxEntitiesPerFrame - 5);
                    
                    ErrorHandler.LogWarning($"Performance degraded, reducing targets: FPS={PerformanceConfig.AimbotTargetFPS}, " +
                        $"Entities={PerformanceConfig.MaxEntitiesPerFrame}");
                }
                else if (metrics.AverageFPS > PerformanceConfig.AimbotTargetFPS * 1.2) // 120% of target
                {
                    // Performance is good, can increase targets
                    PerformanceConfig.AimbotTargetFPS = Math.Min(120, PerformanceConfig.AimbotTargetFPS + 5);
                    PerformanceConfig.MaxEntitiesPerFrame = Math.Min(100, PerformanceConfig.MaxEntitiesPerFrame + 5);
                }

                // Memory pressure adaptation
                var memoryMB = metrics.AverageMemoryUsage / 1024 / 1024;
                if (memoryMB > PerformanceConfig.MemoryPressureThresholdMB)
                {
                    // High memory usage, trigger cleanup
                    _ = Task.Run(() =>
                    {
                        InternalMemory.ClearCache();
                        Core.Entities?.Clear();
                        GC.Collect(1, GCCollectionMode.Optimized);
                    });
                    
                    ErrorHandler.LogWarning($"High memory usage ({memoryMB}MB), triggering cleanup");
                }

                // Cache optimization
                if (metrics.CacheHitRate < 50) // Low hit rate
                {
                    PerformanceConfig.MaxCacheSize = Math.Min(20000, PerformanceConfig.MaxCacheSize + 1000);
                }
                else if (metrics.CacheHitRate > 90) // Very high hit rate
                {
                    PerformanceConfig.MaxCacheSize = Math.Max(5000, PerformanceConfig.MaxCacheSize - 500);
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Adaptive optimization error", ex);
            }
        }

        /// <summary>
        /// Log current performance metrics
        /// </summary>
        private static void LogCurrentMetrics(PerformanceMetrics metrics)
        {
            try
            {
                var memoryMB = metrics.AverageMemoryUsage / 1024 / 1024;
                
                ErrorHandler.LogInfo($"Performance Metrics - FPS: {metrics.AverageFPS:F1}, " +
                    $"Memory: {memoryMB}MB, Threads: {metrics.ActiveThreads}, " +
                    $"Cache Hit Rate: {metrics.CacheHitRate}%");

                // Check for performance issues
                if (metrics.AverageFPS < 20)
                {
                    ErrorHandler.LogWarning("Low FPS detected - consider reducing settings");
                }

                if (memoryMB > 1000) // 1GB
                {
                    ErrorHandler.LogWarning("High memory usage detected");
                }

                if (metrics.ActiveThreads > 50)
                {
                    ErrorHandler.LogWarning("High thread count detected");
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Metrics logging error", ex);
            }
        }

        /// <summary>
        /// Get current performance summary
        /// </summary>
        public static string GetPerformanceSummary()
        {
            try
            {
                if (_metrics.IsEmpty)
                    return "No performance data available";

                var recentMetrics = new List<PerformanceMetrics>();
                foreach (var kvp in _metrics)
                {
                    recentMetrics.Add(kvp.Value);
                }

                if (recentMetrics.Count == 0)
                    return "No recent performance data";

                var avgFPS = recentMetrics.Average(m => m.AverageFPS);
                var avgMemory = recentMetrics.Average(m => m.AverageMemoryUsage) / 1024 / 1024;
                var avgThreads = recentMetrics.Average(m => m.ActiveThreads);
                var avgCacheHitRate = recentMetrics.Average(m => m.CacheHitRate);

                return $"Performance Summary (Last {recentMetrics.Count}s):\n" +
                       $"  Average FPS: {avgFPS:F1}\n" +
                       $"  Average Memory: {avgMemory:F1}MB\n" +
                       $"  Average Threads: {avgThreads:F0}\n" +
                       $"  Average Cache Hit Rate: {avgCacheHitRate:F1}%\n" +
                       $"  Current Settings: Aimbot={PerformanceConfig.AimbotTargetFPS}fps, " +
                       $"Data={PerformanceConfig.DataCollectionTargetFPS}fps, " +
                       $"MaxEntities={PerformanceConfig.MaxEntitiesPerFrame}";
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Error generating performance summary", ex);
                return "Error generating performance summary";
            }
        }

        /// <summary>
        /// Force performance optimization
        /// </summary>
        public static void ForceOptimization()
        {
            try
            {
                ErrorHandler.LogInfo("Forcing performance optimization...");

                // Clear all caches
                InternalMemory.ClearCache();
                Core.Entities?.Clear();

                // Force garbage collection
                GC.Collect(2, GCCollectionMode.Aggressive);
                GC.WaitForPendingFinalizers();
                GC.Collect(2, GCCollectionMode.Aggressive);

                // Reset to optimal settings
                PerformanceConfig.OptimizeForSystem();

                ErrorHandler.LogInfo("Performance optimization completed");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Force optimization error", ex);
            }
        }

        /// <summary>
        /// Check if monitoring is active
        /// </summary>
        public static bool IsMonitoring => _isMonitoring;

        /// <summary>
        /// Get metrics count
        /// </summary>
        public static int MetricsCount => _metrics.Count;
    }

    /// <summary>
    /// Automatic performance tuner
    /// </summary>
    internal static class AutoTuner
    {
        private static Timer _tuningTimer;
        private static volatile bool _isAutoTuning = false;
        private static int _tuningCycles = 0;

        /// <summary>
        /// Start automatic performance tuning
        /// </summary>
        public static void StartAutoTuning()
        {
            if (_isAutoTuning) return;

            try
            {
                _tuningTimer = new Timer(PerformAutoTuning, null,
                    TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
                
                _isAutoTuning = true;
                ErrorHandler.LogInfo("Auto-tuning started");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Failed to start auto-tuning", ex);
            }
        }

        /// <summary>
        /// Stop automatic tuning
        /// </summary>
        public static void StopAutoTuning()
        {
            try
            {
                _tuningTimer?.Dispose();
                _tuningTimer = null;
                _isAutoTuning = false;
                
                ErrorHandler.LogInfo("Auto-tuning stopped");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Auto-tuning stop error", ex);
            }
        }

        /// <summary>
        /// Perform automatic tuning
        /// </summary>
        private static void PerformAutoTuning(object state)
        {
            try
            {
                _tuningCycles++;
                ErrorHandler.LogInfo($"Auto-tuning cycle {_tuningCycles} started");

                // Get current performance metrics
                var summary = RealTimeMonitor.GetPerformanceSummary();
                ErrorHandler.LogInfo($"Current performance: {summary}");

                // Apply system-specific optimizations
                PerformanceConfig.OptimizeForSystem();

                // Apply advanced optimizations
                AdvancedOptimizations.ApplyAllOptimizations();

                ErrorHandler.LogInfo($"Auto-tuning cycle {_tuningCycles} completed");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Auto-tuning error", ex);
            }
        }

        /// <summary>
        /// Check if auto-tuning is active
        /// </summary>
        public static bool IsAutoTuning => _isAutoTuning;

        /// <summary>
        /// Get tuning cycles count
        /// </summary>
        public static int TuningCycles => _tuningCycles;
    }
}
