﻿namespace AotForms
{
    partial class Form1
    {
        /// <summary>
        ///  Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        ///  Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        ///  Required method for Designer support - do not modify
        ///  the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            components = new System.ComponentModel.Container();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges50 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges51 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges48 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges49 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges46 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges47 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges44 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges45 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges42 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges43 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges32 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges33 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges34 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges35 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges36 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges37 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges38 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges39 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges40 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges41 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges30 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges31 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges28 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges29 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges26 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges27 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges24 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges25 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges22 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges23 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges20 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges21 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges18 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges19 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges14 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges15 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges16 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges17 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges10 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges11 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges12 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges13 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges8 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges9 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges6 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges7 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges4 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges5 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges3 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges1 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            Guna.UI2.WinForms.Suite.CustomizableEdges customizableEdges2 = new Guna.UI2.WinForms.Suite.CustomizableEdges();
            guna2BorderlessForm1 = new Guna.UI2.WinForms.Guna2BorderlessForm(components);
            label1 = new Label();
            guna2Separator1 = new Guna.UI2.WinForms.Guna2Separator();
            guna2Separator2 = new Guna.UI2.WinForms.Guna2Separator();
            label2 = new Label();
            guna2vSeparator1 = new Guna.UI2.WinForms.Guna2VSeparator();
            guna2Separator3 = new Guna.UI2.WinForms.Guna2Separator();
            guna2vSeparator2 = new Guna.UI2.WinForms.Guna2VSeparator();
            label3 = new Label();
            guna2Separator4 = new Guna.UI2.WinForms.Guna2Separator();
            label4 = new Label();
            aimbotragecheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label5 = new Label();
            aimbotlegitcheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label6 = new Label();
            norecoilcheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label7 = new Label();
            ingoreknockcheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label8 = new Label();
            label9 = new Label();
            guna2Separator5 = new Guna.UI2.WinForms.Guna2Separator();
            guna2Separator6 = new Guna.UI2.WinForms.Guna2Separator();
            drawcirclecheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label10 = new Label();
            guna2Separator7 = new Guna.UI2.WinForms.Guna2Separator();
            label11 = new Label();
            label12 = new Label();
            label13 = new Label();
            label14 = new Label();
            label15 = new Label();
            label16 = new Label();
            guna2Separator8 = new Guna.UI2.WinForms.Guna2Separator();
            statuslabel = new Label();
            espskeletoncheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label17 = new Label();
            espfillboxcheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label18 = new Label();
            espboxcornercheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label19 = new Label();
            espboxcheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label20 = new Label();
            esplinecheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label21 = new Label();
            guna2vSeparator3 = new Guna.UI2.WinForms.Guna2VSeparator();
            guna2Separator9 = new Guna.UI2.WinForms.Guna2Separator();
            guna2vSeparator4 = new Guna.UI2.WinForms.Guna2VSeparator();
            label22 = new Label();
            espinfocheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label23 = new Label();
            guna2ComboBox1 = new Guna.UI2.WinForms.Guna2ComboBox();
            label24 = new Label();
            espboxcolorpicker = new Guna.UI2.WinForms.Guna2Button();
            espboxcornercolorpicker = new Guna.UI2.WinForms.Guna2Button();
            espfillboxcolorpicker = new Guna.UI2.WinForms.Guna2Button();
            espskeletoncolorpicker = new Guna.UI2.WinForms.Guna2Button();
            guna2Separator10 = new Guna.UI2.WinForms.Guna2Separator();
            guna2Separator11 = new Guna.UI2.WinForms.Guna2Separator();
            label25 = new Label();
            guna2Separator12 = new Guna.UI2.WinForms.Guna2Separator();
            aimbothexcheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            streammodecheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            autorefreshcheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label26 = new Label();
            label27 = new Label();
            renderinglinefixcheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            renderinglagfixcheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label28 = new Label();
            label29 = new Label();
            label30 = new Label();
            pinontop = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            label31 = new Label();
            esplinecolorpicker = new Guna.UI2.WinForms.Guna2Button();
            hookscheckbox = new Guna.UI2.WinForms.Guna2CustomCheckBox();
            guna2CircleButton1 = new Guna.UI2.WinForms.Guna2CircleButton();
            guna2ControlBox1 = new Guna.UI2.WinForms.Guna2ControlBox();
            autorefresh = new System.Windows.Forms.Timer(components);
            aimfovtrackvalue = new Guna.UI2.WinForms.Guna2TrackBar();
            smoothtrackvalue = new Guna.UI2.WinForms.Guna2TrackBar();
            rangetrackvalue = new Guna.UI2.WinForms.Guna2TrackBar();
            SuspendLayout();
            // 
            // guna2BorderlessForm1
            // 
            guna2BorderlessForm1.BorderRadius = 9;
            guna2BorderlessForm1.ContainerControl = this;
            guna2BorderlessForm1.DockForm = false;
            guna2BorderlessForm1.DockIndicatorTransparencyValue = 0.6D;
            guna2BorderlessForm1.DragStartTransparencyValue = 1D;
            guna2BorderlessForm1.HasFormShadow = false;
            guna2BorderlessForm1.ResizeForm = false;
            guna2BorderlessForm1.ShadowColor = Color.Red;
            guna2BorderlessForm1.TransparentWhileDrag = true;
            // 
            // label1
            // 
            label1.AutoSize = true;
            label1.Font = new Font("Segoe UI Black", 14.25F, FontStyle.Bold, GraphicsUnit.Point);
            label1.ForeColor = Color.White;
            label1.Location = new Point(129, 9);
            label1.Name = "label1";
            label1.Size = new Size(119, 25);
            label1.TabIndex = 0;
            label1.Text = "HX CHEATS";
            // 
            // guna2Separator1
            // 
            guna2Separator1.BackColor = Color.Transparent;
            guna2Separator1.FillColor = Color.Blue;
            guna2Separator1.FillThickness = 2;
            guna2Separator1.Location = new Point(12, 52);
            guna2Separator1.Name = "guna2Separator1";
            guna2Separator1.Size = new Size(166, 10);
            guna2Separator1.TabIndex = 1;
            guna2Separator1.UseTransparentBackground = true;
            // 
            // guna2Separator2
            // 
            guna2Separator2.BackColor = Color.Transparent;
            guna2Separator2.FillColor = Color.Blue;
            guna2Separator2.FillThickness = 2;
            guna2Separator2.Location = new Point(202, 52);
            guna2Separator2.Name = "guna2Separator2";
            guna2Separator2.Size = new Size(166, 10);
            guna2Separator2.TabIndex = 2;
            guna2Separator2.UseTransparentBackground = true;
            // 
            // label2
            // 
            label2.AutoSize = true;
            label2.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label2.ForeColor = Color.White;
            label2.Location = new Point(14, 61);
            label2.Name = "label2";
            label2.Size = new Size(30, 13);
            label2.TabIndex = 3;
            label2.Text = "AIM";
            // 
            // guna2vSeparator1
            // 
            guna2vSeparator1.BackColor = Color.Transparent;
            guna2vSeparator1.FillColor = Color.FromArgb(30, 30, 30);
            guna2vSeparator1.Location = new Point(7, 57);
            guna2vSeparator1.Name = "guna2vSeparator1";
            guna2vSeparator1.Size = new Size(10, 356);
            guna2vSeparator1.TabIndex = 5;
            guna2vSeparator1.UseTransparentBackground = true;
            // 
            // guna2Separator3
            // 
            guna2Separator3.BackColor = Color.Transparent;
            guna2Separator3.FillColor = Color.FromArgb(30, 30, 30);
            guna2Separator3.Location = new Point(12, 73);
            guna2Separator3.Name = "guna2Separator3";
            guna2Separator3.Size = new Size(166, 10);
            guna2Separator3.TabIndex = 6;
            guna2Separator3.UseTransparentBackground = true;
            // 
            // guna2vSeparator2
            // 
            guna2vSeparator2.BackColor = Color.Transparent;
            guna2vSeparator2.FillColor = Color.FromArgb(30, 30, 30);
            guna2vSeparator2.Location = new Point(172, 57);
            guna2vSeparator2.Name = "guna2vSeparator2";
            guna2vSeparator2.Size = new Size(10, 356);
            guna2vSeparator2.TabIndex = 7;
            guna2vSeparator2.UseTransparentBackground = true;
            // 
            // label3
            // 
            label3.AutoSize = true;
            label3.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label3.ForeColor = Color.DarkGray;
            label3.Location = new Point(17, 86);
            label3.Name = "label3";
            label3.Size = new Size(78, 13);
            label3.TabIndex = 8;
            label3.Text = "Enable Hooks";
            // 
            // guna2Separator4
            // 
            guna2Separator4.BackColor = Color.Transparent;
            guna2Separator4.FillColor = Color.FromArgb(30, 30, 30);
            guna2Separator4.Location = new Point(12, 102);
            guna2Separator4.Name = "guna2Separator4";
            guna2Separator4.Size = new Size(166, 10);
            guna2Separator4.TabIndex = 10;
            guna2Separator4.UseTransparentBackground = true;
            // 
            // label4
            // 
            label4.AutoSize = true;
            label4.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label4.ForeColor = Color.DarkGray;
            label4.Location = new Point(17, 116);
            label4.Name = "label4";
            label4.Size = new Size(70, 13);
            label4.TabIndex = 11;
            label4.Text = "AimBot HeX";
            // 
            // aimbotragecheckbox
            // 
            aimbotragecheckbox.Animated = true;
            aimbotragecheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            aimbotragecheckbox.CheckedState.BorderRadius = 2;
            aimbotragecheckbox.CheckedState.BorderThickness = 0;
            aimbotragecheckbox.CheckedState.FillColor = Color.Red;
            aimbotragecheckbox.CustomizableEdges = customizableEdges50;
            aimbotragecheckbox.Location = new Point(152, 137);
            aimbotragecheckbox.Name = "aimbotragecheckbox";
            aimbotragecheckbox.ShadowDecoration.CustomizableEdges = customizableEdges51;
            aimbotragecheckbox.Size = new Size(20, 20);
            aimbotragecheckbox.TabIndex = 14;
            aimbotragecheckbox.Text = "guna2CustomCheckBox3";
            aimbotragecheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            aimbotragecheckbox.UncheckedState.BorderRadius = 2;
            aimbotragecheckbox.UncheckedState.BorderThickness = 1;
            aimbotragecheckbox.UncheckedState.FillColor = Color.Transparent;
            aimbotragecheckbox.Click += aimbotragecheckbox_Click;
            // 
            // label5
            // 
            label5.AutoSize = true;
            label5.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label5.ForeColor = Color.DarkGray;
            label5.Location = new Point(17, 140);
            label5.Name = "label5";
            label5.Size = new Size(75, 13);
            label5.TabIndex = 13;
            label5.Text = "AimBot Rage";
            // 
            // aimbotlegitcheckbox
            // 
            aimbotlegitcheckbox.Animated = true;
            aimbotlegitcheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            aimbotlegitcheckbox.CheckedState.BorderRadius = 2;
            aimbotlegitcheckbox.CheckedState.BorderThickness = 0;
            aimbotlegitcheckbox.CheckedState.FillColor = Color.Red;
            aimbotlegitcheckbox.CustomizableEdges = customizableEdges48;
            aimbotlegitcheckbox.Location = new Point(152, 161);
            aimbotlegitcheckbox.Name = "aimbotlegitcheckbox";
            aimbotlegitcheckbox.ShadowDecoration.CustomizableEdges = customizableEdges49;
            aimbotlegitcheckbox.Size = new Size(20, 20);
            aimbotlegitcheckbox.TabIndex = 16;
            aimbotlegitcheckbox.Text = "guna2CustomCheckBox4";
            aimbotlegitcheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            aimbotlegitcheckbox.UncheckedState.BorderRadius = 2;
            aimbotlegitcheckbox.UncheckedState.BorderThickness = 1;
            aimbotlegitcheckbox.UncheckedState.FillColor = Color.Transparent;
            aimbotlegitcheckbox.Click += aimbotlegitcheckbox_Click;
            // 
            // label6
            // 
            label6.AutoSize = true;
            label6.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label6.ForeColor = Color.DarkGray;
            label6.Location = new Point(17, 164);
            label6.Name = "label6";
            label6.Size = new Size(75, 13);
            label6.TabIndex = 15;
            label6.Text = "AimBot Legit";
            // 
            // norecoilcheckbox
            // 
            norecoilcheckbox.Animated = true;
            norecoilcheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            norecoilcheckbox.CheckedState.BorderRadius = 2;
            norecoilcheckbox.CheckedState.BorderThickness = 0;
            norecoilcheckbox.CheckedState.FillColor = Color.Red;
            norecoilcheckbox.CustomizableEdges = customizableEdges46;
            norecoilcheckbox.Location = new Point(152, 185);
            norecoilcheckbox.Name = "norecoilcheckbox";
            norecoilcheckbox.ShadowDecoration.CustomizableEdges = customizableEdges47;
            norecoilcheckbox.Size = new Size(20, 20);
            norecoilcheckbox.TabIndex = 18;
            norecoilcheckbox.Text = "guna2CustomCheckBox5";
            norecoilcheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            norecoilcheckbox.UncheckedState.BorderRadius = 2;
            norecoilcheckbox.UncheckedState.BorderThickness = 1;
            norecoilcheckbox.UncheckedState.FillColor = Color.Transparent;
            norecoilcheckbox.Click += norecoilcheckbox_Click;
            // 
            // label7
            // 
            label7.AutoSize = true;
            label7.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label7.ForeColor = Color.DarkGray;
            label7.Location = new Point(17, 188);
            label7.Name = "label7";
            label7.Size = new Size(57, 13);
            label7.TabIndex = 17;
            label7.Text = "No Recoil";
            // 
            // ingoreknockcheckbox
            // 
            ingoreknockcheckbox.Animated = true;
            ingoreknockcheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            ingoreknockcheckbox.CheckedState.BorderRadius = 2;
            ingoreknockcheckbox.CheckedState.BorderThickness = 0;
            ingoreknockcheckbox.CheckedState.FillColor = Color.Red;
            ingoreknockcheckbox.CustomizableEdges = customizableEdges44;
            ingoreknockcheckbox.Location = new Point(152, 209);
            ingoreknockcheckbox.Name = "ingoreknockcheckbox";
            ingoreknockcheckbox.ShadowDecoration.CustomizableEdges = customizableEdges45;
            ingoreknockcheckbox.Size = new Size(20, 20);
            ingoreknockcheckbox.TabIndex = 20;
            ingoreknockcheckbox.Text = "guna2CustomCheckBox6";
            ingoreknockcheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            ingoreknockcheckbox.UncheckedState.BorderRadius = 2;
            ingoreknockcheckbox.UncheckedState.BorderThickness = 1;
            ingoreknockcheckbox.UncheckedState.FillColor = Color.Transparent;
            ingoreknockcheckbox.Click += ingoreknockcheckbox_Click;
            // 
            // label8
            // 
            label8.AutoSize = true;
            label8.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label8.ForeColor = Color.DarkGray;
            label8.Location = new Point(17, 212);
            label8.Name = "label8";
            label8.Size = new Size(89, 13);
            label8.TabIndex = 19;
            label8.Text = "Ignore Knocked";
            // 
            // label9
            // 
            label9.AutoSize = true;
            label9.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label9.ForeColor = Color.White;
            label9.Location = new Point(14, 242);
            label9.Name = "label9";
            label9.Size = new Size(52, 13);
            label9.TabIndex = 22;
            label9.Text = "AIMFOV";
            // 
            // guna2Separator5
            // 
            guna2Separator5.BackColor = Color.Transparent;
            guna2Separator5.FillColor = Color.Blue;
            guna2Separator5.FillThickness = 2;
            guna2Separator5.Location = new Point(12, 233);
            guna2Separator5.Name = "guna2Separator5";
            guna2Separator5.Size = new Size(166, 10);
            guna2Separator5.TabIndex = 21;
            guna2Separator5.UseTransparentBackground = true;
            // 
            // guna2Separator6
            // 
            guna2Separator6.BackColor = Color.Transparent;
            guna2Separator6.FillColor = Color.FromArgb(30, 30, 30);
            guna2Separator6.Location = new Point(12, 254);
            guna2Separator6.Name = "guna2Separator6";
            guna2Separator6.Size = new Size(166, 10);
            guna2Separator6.TabIndex = 23;
            guna2Separator6.UseTransparentBackground = true;
            // 
            // drawcirclecheckbox
            // 
            drawcirclecheckbox.Animated = true;
            drawcirclecheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            drawcirclecheckbox.CheckedState.BorderRadius = 2;
            drawcirclecheckbox.CheckedState.BorderThickness = 0;
            drawcirclecheckbox.CheckedState.FillColor = Color.Red;
            drawcirclecheckbox.CustomizableEdges = customizableEdges42;
            drawcirclecheckbox.Location = new Point(152, 264);
            drawcirclecheckbox.Name = "drawcirclecheckbox";
            drawcirclecheckbox.ShadowDecoration.CustomizableEdges = customizableEdges43;
            drawcirclecheckbox.Size = new Size(20, 20);
            drawcirclecheckbox.TabIndex = 25;
            drawcirclecheckbox.Text = "guna2CustomCheckBox7";
            drawcirclecheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            drawcirclecheckbox.UncheckedState.BorderRadius = 2;
            drawcirclecheckbox.UncheckedState.BorderThickness = 1;
            drawcirclecheckbox.UncheckedState.FillColor = Color.Transparent;
            drawcirclecheckbox.Click += drawcirclecheckbox_Click;
            // 
            // label10
            // 
            label10.AutoSize = true;
            label10.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label10.ForeColor = Color.DarkGray;
            label10.Location = new Point(17, 267);
            label10.Name = "label10";
            label10.Size = new Size(87, 13);
            label10.TabIndex = 24;
            label10.Text = "Draw Circle Fov";
            // 
            // guna2Separator7
            // 
            guna2Separator7.BackColor = Color.Transparent;
            guna2Separator7.FillColor = Color.FromArgb(30, 30, 30);
            guna2Separator7.Location = new Point(12, 284);
            guna2Separator7.Name = "guna2Separator7";
            guna2Separator7.Size = new Size(166, 10);
            guna2Separator7.TabIndex = 26;
            guna2Separator7.UseTransparentBackground = true;
            // 
            // label11
            // 
            label11.AutoSize = true;
            label11.BackColor = Color.Transparent;
            label11.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label11.ForeColor = Color.DarkGray;
            label11.Location = new Point(16, 294);
            label11.Name = "label11";
            label11.Size = new Size(47, 13);
            label11.TabIndex = 60;
            label11.Text = "AimFov";
            // 
            // label12
            // 
            label12.AutoSize = true;
            label12.BackColor = Color.Transparent;
            label12.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label12.ForeColor = Color.DarkGray;
            label12.Location = new Point(138, 294);
            label12.Name = "label12";
            label12.Size = new Size(33, 13);
            label12.TabIndex = 61;
            label12.Text = "(100)";
            // 
            // label13
            // 
            label13.AutoSize = true;
            label13.BackColor = Color.Transparent;
            label13.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label13.ForeColor = Color.DarkGray;
            label13.Location = new Point(138, 331);
            label13.Name = "label13";
            label13.Size = new Size(21, 13);
            label13.TabIndex = 63;
            label13.Text = "(0)";
            // 
            // label14
            // 
            label14.AutoSize = true;
            label14.BackColor = Color.Transparent;
            label14.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label14.ForeColor = Color.DarkGray;
            label14.Location = new Point(16, 331);
            label14.Name = "label14";
            label14.Size = new Size(48, 13);
            label14.TabIndex = 62;
            label14.Text = "Smooth";
            // 
            // label15
            // 
            label15.AutoSize = true;
            label15.BackColor = Color.Transparent;
            label15.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label15.ForeColor = Color.DarkGray;
            label15.Location = new Point(138, 367);
            label15.Name = "label15";
            label15.Size = new Size(33, 13);
            label15.TabIndex = 65;
            label15.Text = "(100)";
            // 
            // label16
            // 
            label16.AutoSize = true;
            label16.BackColor = Color.Transparent;
            label16.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label16.ForeColor = Color.DarkGray;
            label16.Location = new Point(16, 367);
            label16.Name = "label16";
            label16.Size = new Size(64, 13);
            label16.TabIndex = 64;
            label16.Text = "Aim Range";
            // 
            // guna2Separator8
            // 
            guna2Separator8.BackColor = Color.Transparent;
            guna2Separator8.FillColor = Color.FromArgb(30, 30, 30);
            guna2Separator8.Location = new Point(12, 407);
            guna2Separator8.Name = "guna2Separator8";
            guna2Separator8.Size = new Size(166, 10);
            guna2Separator8.TabIndex = 66;
            guna2Separator8.UseTransparentBackground = true;
            // 
            // statuslabel
            // 
            statuslabel.AutoSize = true;
            statuslabel.Font = new Font("Segoe UI", 8F, FontStyle.Bold, GraphicsUnit.Point);
            statuslabel.ForeColor = Color.Lime;
            statuslabel.Location = new Point(7, 419);
            statuslabel.Name = "statuslabel";
            statuslabel.Size = new Size(120, 13);
            statuslabel.TabIndex = 67;
            statuslabel.Text = "Status : Login Success";
            // 
            // espskeletoncheckbox
            // 
            espskeletoncheckbox.Animated = true;
            espskeletoncheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            espskeletoncheckbox.CheckedState.BorderRadius = 2;
            espskeletoncheckbox.CheckedState.BorderThickness = 0;
            espskeletoncheckbox.CheckedState.FillColor = Color.Red;
            espskeletoncheckbox.CustomizableEdges = customizableEdges32;
            espskeletoncheckbox.Location = new Point(342, 179);
            espskeletoncheckbox.Name = "espskeletoncheckbox";
            espskeletoncheckbox.ShadowDecoration.CustomizableEdges = customizableEdges33;
            espskeletoncheckbox.Size = new Size(20, 20);
            espskeletoncheckbox.TabIndex = 81;
            espskeletoncheckbox.Text = "guna2CustomCheckBox8";
            espskeletoncheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            espskeletoncheckbox.UncheckedState.BorderRadius = 2;
            espskeletoncheckbox.UncheckedState.BorderThickness = 1;
            espskeletoncheckbox.UncheckedState.FillColor = Color.Transparent;
            espskeletoncheckbox.Click += espskeletoncheckbox_Click;
            // 
            // label17
            // 
            label17.AutoSize = true;
            label17.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label17.ForeColor = Color.DarkGray;
            label17.Location = new Point(207, 182);
            label17.Name = "label17";
            label17.Size = new Size(73, 13);
            label17.TabIndex = 80;
            label17.Text = "Esp Skeleton";
            // 
            // espfillboxcheckbox
            // 
            espfillboxcheckbox.Animated = true;
            espfillboxcheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            espfillboxcheckbox.CheckedState.BorderRadius = 2;
            espfillboxcheckbox.CheckedState.BorderThickness = 0;
            espfillboxcheckbox.CheckedState.FillColor = Color.Red;
            espfillboxcheckbox.CustomizableEdges = customizableEdges34;
            espfillboxcheckbox.Location = new Point(342, 155);
            espfillboxcheckbox.Name = "espfillboxcheckbox";
            espfillboxcheckbox.ShadowDecoration.CustomizableEdges = customizableEdges35;
            espfillboxcheckbox.Size = new Size(20, 20);
            espfillboxcheckbox.TabIndex = 79;
            espfillboxcheckbox.Text = "guna2CustomCheckBox9";
            espfillboxcheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            espfillboxcheckbox.UncheckedState.BorderRadius = 2;
            espfillboxcheckbox.UncheckedState.BorderThickness = 1;
            espfillboxcheckbox.UncheckedState.FillColor = Color.Transparent;
            espfillboxcheckbox.Click += espfillboxcheckbox_Click;
            // 
            // label18
            // 
            label18.AutoSize = true;
            label18.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label18.ForeColor = Color.DarkGray;
            label18.Location = new Point(207, 158);
            label18.Name = "label18";
            label18.Size = new Size(66, 13);
            label18.TabIndex = 78;
            label18.Text = "Esp Fill Box";
            // 
            // espboxcornercheckbox
            // 
            espboxcornercheckbox.Animated = true;
            espboxcornercheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            espboxcornercheckbox.CheckedState.BorderRadius = 2;
            espboxcornercheckbox.CheckedState.BorderThickness = 0;
            espboxcornercheckbox.CheckedState.FillColor = Color.Red;
            espboxcornercheckbox.CustomizableEdges = customizableEdges36;
            espboxcornercheckbox.Location = new Point(342, 131);
            espboxcornercheckbox.Name = "espboxcornercheckbox";
            espboxcornercheckbox.ShadowDecoration.CustomizableEdges = customizableEdges37;
            espboxcornercheckbox.Size = new Size(20, 20);
            espboxcornercheckbox.TabIndex = 77;
            espboxcornercheckbox.Text = "guna2CustomCheckBox10";
            espboxcornercheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            espboxcornercheckbox.UncheckedState.BorderRadius = 2;
            espboxcornercheckbox.UncheckedState.BorderThickness = 1;
            espboxcornercheckbox.UncheckedState.FillColor = Color.Transparent;
            espboxcornercheckbox.Click += espboxcornercheckbox_Click;
            // 
            // label19
            // 
            label19.AutoSize = true;
            label19.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label19.ForeColor = Color.DarkGray;
            label19.Location = new Point(207, 134);
            label19.Name = "label19";
            label19.Size = new Size(86, 13);
            label19.TabIndex = 76;
            label19.Text = "Esp Box Corner";
            // 
            // espboxcheckbox
            // 
            espboxcheckbox.Animated = true;
            espboxcheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            espboxcheckbox.CheckedState.BorderRadius = 2;
            espboxcheckbox.CheckedState.BorderThickness = 0;
            espboxcheckbox.CheckedState.FillColor = Color.Red;
            espboxcheckbox.CustomizableEdges = customizableEdges38;
            espboxcheckbox.Location = new Point(342, 107);
            espboxcheckbox.Name = "espboxcheckbox";
            espboxcheckbox.ShadowDecoration.CustomizableEdges = customizableEdges39;
            espboxcheckbox.Size = new Size(20, 20);
            espboxcheckbox.TabIndex = 75;
            espboxcheckbox.Text = "guna2CustomCheckBox11";
            espboxcheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            espboxcheckbox.UncheckedState.BorderRadius = 2;
            espboxcheckbox.UncheckedState.BorderThickness = 1;
            espboxcheckbox.UncheckedState.FillColor = Color.Transparent;
            espboxcheckbox.Click += espboxcheckbox_Click;
            // 
            // label20
            // 
            label20.AutoSize = true;
            label20.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label20.ForeColor = Color.DarkGray;
            label20.Location = new Point(207, 110);
            label20.Name = "label20";
            label20.Size = new Size(48, 13);
            label20.TabIndex = 74;
            label20.Text = "Esp Box";
            // 
            // esplinecheckbox
            // 
            esplinecheckbox.Animated = true;
            esplinecheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            esplinecheckbox.CheckedState.BorderRadius = 2;
            esplinecheckbox.CheckedState.BorderThickness = 0;
            esplinecheckbox.CheckedState.FillColor = Color.Red;
            esplinecheckbox.CustomizableEdges = customizableEdges40;
            esplinecheckbox.Location = new Point(342, 83);
            esplinecheckbox.Name = "esplinecheckbox";
            esplinecheckbox.ShadowDecoration.CustomizableEdges = customizableEdges41;
            esplinecheckbox.Size = new Size(20, 20);
            esplinecheckbox.TabIndex = 73;
            esplinecheckbox.Text = "guna2CustomCheckBox12";
            esplinecheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            esplinecheckbox.UncheckedState.BorderRadius = 2;
            esplinecheckbox.UncheckedState.BorderThickness = 1;
            esplinecheckbox.UncheckedState.FillColor = Color.Transparent;
            esplinecheckbox.Click += esplinecheckbox_Click;
            // 
            // label21
            // 
            label21.AutoSize = true;
            label21.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label21.ForeColor = Color.DarkGray;
            label21.Location = new Point(207, 86);
            label21.Name = "label21";
            label21.Size = new Size(50, 13);
            label21.TabIndex = 72;
            label21.Text = "Esp Line";
            // 
            // guna2vSeparator3
            // 
            guna2vSeparator3.BackColor = Color.Transparent;
            guna2vSeparator3.FillColor = Color.FromArgb(30, 30, 30);
            guna2vSeparator3.Location = new Point(362, 56);
            guna2vSeparator3.Name = "guna2vSeparator3";
            guna2vSeparator3.Size = new Size(10, 356);
            guna2vSeparator3.TabIndex = 71;
            guna2vSeparator3.UseTransparentBackground = true;
            // 
            // guna2Separator9
            // 
            guna2Separator9.BackColor = Color.Transparent;
            guna2Separator9.FillColor = Color.FromArgb(30, 30, 30);
            guna2Separator9.Location = new Point(202, 72);
            guna2Separator9.Name = "guna2Separator9";
            guna2Separator9.Size = new Size(166, 10);
            guna2Separator9.TabIndex = 70;
            guna2Separator9.UseTransparentBackground = true;
            // 
            // guna2vSeparator4
            // 
            guna2vSeparator4.BackColor = Color.Transparent;
            guna2vSeparator4.FillColor = Color.FromArgb(30, 30, 30);
            guna2vSeparator4.Location = new Point(197, 56);
            guna2vSeparator4.Name = "guna2vSeparator4";
            guna2vSeparator4.Size = new Size(10, 356);
            guna2vSeparator4.TabIndex = 69;
            guna2vSeparator4.UseTransparentBackground = true;
            // 
            // label22
            // 
            label22.AutoSize = true;
            label22.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label22.ForeColor = Color.White;
            label22.Location = new Point(204, 60);
            label22.Name = "label22";
            label22.Size = new Size(63, 13);
            label22.TabIndex = 68;
            label22.Text = "ESP MENU";
            // 
            // espinfocheckbox
            // 
            espinfocheckbox.Animated = true;
            espinfocheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            espinfocheckbox.CheckedState.BorderRadius = 2;
            espinfocheckbox.CheckedState.BorderThickness = 0;
            espinfocheckbox.CheckedState.FillColor = Color.Red;
            espinfocheckbox.CustomizableEdges = customizableEdges30;
            espinfocheckbox.Location = new Point(342, 203);
            espinfocheckbox.Name = "espinfocheckbox";
            espinfocheckbox.ShadowDecoration.CustomizableEdges = customizableEdges31;
            espinfocheckbox.Size = new Size(20, 20);
            espinfocheckbox.TabIndex = 83;
            espinfocheckbox.Text = "guna2CustomCheckBox13";
            espinfocheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            espinfocheckbox.UncheckedState.BorderRadius = 2;
            espinfocheckbox.UncheckedState.BorderThickness = 1;
            espinfocheckbox.UncheckedState.FillColor = Color.Transparent;
            espinfocheckbox.Click += espinfocheckbox_Click;
            // 
            // label23
            // 
            label23.AutoSize = true;
            label23.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label23.ForeColor = Color.DarkGray;
            label23.Location = new Point(207, 206);
            label23.Name = "label23";
            label23.Size = new Size(90, 13);
            label23.TabIndex = 82;
            label23.Text = "Esp Information";
            // 
            // guna2ComboBox1
            // 
            guna2ComboBox1.BackColor = Color.Transparent;
            guna2ComboBox1.BorderColor = Color.FromArgb(30, 30, 30);
            guna2ComboBox1.BorderRadius = 3;
            guna2ComboBox1.CustomizableEdges = customizableEdges28;
            guna2ComboBox1.DisabledState.FillColor = Color.Black;
            guna2ComboBox1.DrawMode = DrawMode.OwnerDrawFixed;
            guna2ComboBox1.DropDownStyle = ComboBoxStyle.DropDownList;
            guna2ComboBox1.FillColor = Color.Black;
            guna2ComboBox1.FocusedColor = Color.FromArgb(30, 30, 30);
            guna2ComboBox1.FocusedState.BorderColor = Color.FromArgb(30, 30, 30);
            guna2ComboBox1.FocusedState.FillColor = Color.Black;
            guna2ComboBox1.Font = new Font("Segoe UI", 10F, FontStyle.Regular, GraphicsUnit.Point);
            guna2ComboBox1.ForeColor = Color.DarkGray;
            guna2ComboBox1.HoverState.FillColor = Color.Black;
            guna2ComboBox1.ItemHeight = 20;
            guna2ComboBox1.Items.AddRange(new object[] { "Up", "Bottom", "Left", "Right" });
            guna2ComboBox1.Location = new Point(264, 229);
            guna2ComboBox1.MaxDropDownItems = 4;
            guna2ComboBox1.Name = "guna2ComboBox1";
            guna2ComboBox1.ShadowDecoration.CustomizableEdges = customizableEdges29;
            guna2ComboBox1.Size = new Size(98, 26);
            guna2ComboBox1.TabIndex = 84;
            guna2ComboBox1.SelectedIndexChanged += guna2ComboBox1_SelectedIndexChanged;
            // 
            // label24
            // 
            label24.AutoSize = true;
            label24.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label24.ForeColor = Color.DarkGray;
            label24.Location = new Point(208, 234);
            label24.Name = "label24";
            label24.Size = new Size(50, 13);
            label24.TabIndex = 85;
            label24.Text = "Esp Line";
            // 
            // espboxcolorpicker
            // 
            espboxcolorpicker.BorderRadius = 2;
            espboxcolorpicker.CustomizableEdges = customizableEdges26;
            espboxcolorpicker.DisabledState.BorderColor = Color.DarkGray;
            espboxcolorpicker.DisabledState.CustomBorderColor = Color.DarkGray;
            espboxcolorpicker.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            espboxcolorpicker.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            espboxcolorpicker.FillColor = Color.White;
            espboxcolorpicker.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            espboxcolorpicker.ForeColor = Color.White;
            espboxcolorpicker.Location = new Point(316, 107);
            espboxcolorpicker.Name = "espboxcolorpicker";
            espboxcolorpicker.ShadowDecoration.CustomizableEdges = customizableEdges27;
            espboxcolorpicker.Size = new Size(20, 20);
            espboxcolorpicker.TabIndex = 87;
            espboxcolorpicker.Click += espboxcolorpicker_Click;
            // 
            // espboxcornercolorpicker
            // 
            espboxcornercolorpicker.BorderRadius = 2;
            espboxcornercolorpicker.CustomizableEdges = customizableEdges24;
            espboxcornercolorpicker.DisabledState.BorderColor = Color.DarkGray;
            espboxcornercolorpicker.DisabledState.CustomBorderColor = Color.DarkGray;
            espboxcornercolorpicker.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            espboxcornercolorpicker.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            espboxcornercolorpicker.FillColor = Color.White;
            espboxcornercolorpicker.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            espboxcornercolorpicker.ForeColor = Color.White;
            espboxcornercolorpicker.Location = new Point(316, 131);
            espboxcornercolorpicker.Name = "espboxcornercolorpicker";
            espboxcornercolorpicker.ShadowDecoration.CustomizableEdges = customizableEdges25;
            espboxcornercolorpicker.Size = new Size(20, 20);
            espboxcornercolorpicker.TabIndex = 88;
            espboxcornercolorpicker.Click += espboxcornercolorpicker_Click;
            // 
            // espfillboxcolorpicker
            // 
            espfillboxcolorpicker.BorderRadius = 2;
            espfillboxcolorpicker.CustomizableEdges = customizableEdges22;
            espfillboxcolorpicker.DisabledState.BorderColor = Color.DarkGray;
            espfillboxcolorpicker.DisabledState.CustomBorderColor = Color.DarkGray;
            espfillboxcolorpicker.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            espfillboxcolorpicker.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            espfillboxcolorpicker.FillColor = Color.White;
            espfillboxcolorpicker.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            espfillboxcolorpicker.ForeColor = Color.White;
            espfillboxcolorpicker.Location = new Point(316, 155);
            espfillboxcolorpicker.Name = "espfillboxcolorpicker";
            espfillboxcolorpicker.ShadowDecoration.CustomizableEdges = customizableEdges23;
            espfillboxcolorpicker.Size = new Size(20, 20);
            espfillboxcolorpicker.TabIndex = 89;
            espfillboxcolorpicker.Click += espfillboxcolorpicker_Click;
            // 
            // espskeletoncolorpicker
            // 
            espskeletoncolorpicker.BorderRadius = 2;
            espskeletoncolorpicker.CustomizableEdges = customizableEdges20;
            espskeletoncolorpicker.DisabledState.BorderColor = Color.DarkGray;
            espskeletoncolorpicker.DisabledState.CustomBorderColor = Color.DarkGray;
            espskeletoncolorpicker.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            espskeletoncolorpicker.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            espskeletoncolorpicker.FillColor = Color.White;
            espskeletoncolorpicker.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            espskeletoncolorpicker.ForeColor = Color.White;
            espskeletoncolorpicker.Location = new Point(316, 179);
            espskeletoncolorpicker.Name = "espskeletoncolorpicker";
            espskeletoncolorpicker.ShadowDecoration.CustomizableEdges = customizableEdges21;
            espskeletoncolorpicker.Size = new Size(20, 20);
            espskeletoncolorpicker.TabIndex = 90;
            espskeletoncolorpicker.Click += espskeletoncolorpicker_Click;
            // 
            // guna2Separator10
            // 
            guna2Separator10.BackColor = Color.Transparent;
            guna2Separator10.FillColor = Color.FromArgb(30, 30, 30);
            guna2Separator10.Location = new Point(202, 406);
            guna2Separator10.Name = "guna2Separator10";
            guna2Separator10.Size = new Size(166, 10);
            guna2Separator10.TabIndex = 91;
            guna2Separator10.UseTransparentBackground = true;
            // 
            // guna2Separator11
            // 
            guna2Separator11.BackColor = Color.Transparent;
            guna2Separator11.FillColor = Color.FromArgb(30, 30, 30);
            guna2Separator11.Location = new Point(202, 282);
            guna2Separator11.Name = "guna2Separator11";
            guna2Separator11.Size = new Size(166, 10);
            guna2Separator11.TabIndex = 94;
            guna2Separator11.UseTransparentBackground = true;
            // 
            // label25
            // 
            label25.AutoSize = true;
            label25.Font = new Font("Segoe UI Black", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label25.ForeColor = Color.White;
            label25.Location = new Point(204, 270);
            label25.Name = "label25";
            label25.Size = new Size(44, 13);
            label25.TabIndex = 93;
            label25.Text = "EXTRA";
            // 
            // guna2Separator12
            // 
            guna2Separator12.BackColor = Color.Transparent;
            guna2Separator12.FillColor = Color.Blue;
            guna2Separator12.FillThickness = 2;
            guna2Separator12.Location = new Point(202, 261);
            guna2Separator12.Name = "guna2Separator12";
            guna2Separator12.Size = new Size(166, 10);
            guna2Separator12.TabIndex = 92;
            guna2Separator12.UseTransparentBackground = true;
            // 
            // aimbothexcheckbox
            // 
            aimbothexcheckbox.Animated = true;
            aimbothexcheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            aimbothexcheckbox.CheckedState.BorderRadius = 2;
            aimbothexcheckbox.CheckedState.BorderThickness = 0;
            aimbothexcheckbox.CheckedState.FillColor = Color.Red;
            aimbothexcheckbox.CustomizableEdges = customizableEdges18;
            aimbothexcheckbox.Location = new Point(152, 113);
            aimbothexcheckbox.Name = "aimbothexcheckbox";
            aimbothexcheckbox.ShadowDecoration.CustomizableEdges = customizableEdges19;
            aimbothexcheckbox.Size = new Size(20, 20);
            aimbothexcheckbox.TabIndex = 95;
            aimbothexcheckbox.Text = "guna2CustomCheckBox2";
            aimbothexcheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            aimbothexcheckbox.UncheckedState.BorderRadius = 2;
            aimbothexcheckbox.UncheckedState.BorderThickness = 1;
            aimbothexcheckbox.UncheckedState.FillColor = Color.Transparent;
            aimbothexcheckbox.Click += aimbothexcheckbox_Click;
            // 
            // streammodecheckbox
            // 
            streammodecheckbox.Animated = true;
            streammodecheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            streammodecheckbox.CheckedState.BorderRadius = 2;
            streammodecheckbox.CheckedState.BorderThickness = 0;
            streammodecheckbox.CheckedState.FillColor = Color.Red;
            streammodecheckbox.CustomizableEdges = customizableEdges14;
            streammodecheckbox.Location = new Point(342, 292);
            streammodecheckbox.Name = "streammodecheckbox";
            streammodecheckbox.ShadowDecoration.CustomizableEdges = customizableEdges15;
            streammodecheckbox.Size = new Size(20, 20);
            streammodecheckbox.TabIndex = 99;
            streammodecheckbox.Text = "guna2CustomCheckBox2";
            streammodecheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            streammodecheckbox.UncheckedState.BorderRadius = 2;
            streammodecheckbox.UncheckedState.BorderThickness = 1;
            streammodecheckbox.UncheckedState.FillColor = Color.Transparent;
            streammodecheckbox.Click += streammodecheckbox_Click;
            // 
            // autorefreshcheckbox
            // 
            autorefreshcheckbox.Animated = true;
            autorefreshcheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            autorefreshcheckbox.CheckedState.BorderRadius = 2;
            autorefreshcheckbox.CheckedState.BorderThickness = 0;
            autorefreshcheckbox.CheckedState.FillColor = Color.Red;
            autorefreshcheckbox.CustomizableEdges = customizableEdges16;
            autorefreshcheckbox.Location = new Point(342, 316);
            autorefreshcheckbox.Name = "autorefreshcheckbox";
            autorefreshcheckbox.ShadowDecoration.CustomizableEdges = customizableEdges17;
            autorefreshcheckbox.Size = new Size(20, 20);
            autorefreshcheckbox.TabIndex = 98;
            autorefreshcheckbox.Text = "guna2CustomCheckBox3";
            autorefreshcheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            autorefreshcheckbox.UncheckedState.BorderRadius = 2;
            autorefreshcheckbox.UncheckedState.BorderThickness = 1;
            autorefreshcheckbox.UncheckedState.FillColor = Color.Transparent;
            autorefreshcheckbox.Click += autorefreshcheckbox_Click;
            // 
            // label26
            // 
            label26.AutoSize = true;
            label26.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label26.ForeColor = Color.DarkGray;
            label26.Location = new Point(207, 319);
            label26.Name = "label26";
            label26.Size = new Size(89, 13);
            label26.TabIndex = 97;
            label26.Text = "Auto Refresh 1s";
            // 
            // label27
            // 
            label27.AutoSize = true;
            label27.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label27.ForeColor = Color.DarkGray;
            label27.Location = new Point(207, 295);
            label27.Name = "label27";
            label27.Size = new Size(77, 13);
            label27.TabIndex = 96;
            label27.Text = "Stream Mode";
            // 
            // renderinglinefixcheckbox
            // 
            renderinglinefixcheckbox.Animated = true;
            renderinglinefixcheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            renderinglinefixcheckbox.CheckedState.BorderRadius = 2;
            renderinglinefixcheckbox.CheckedState.BorderThickness = 0;
            renderinglinefixcheckbox.CheckedState.FillColor = Color.Red;
            renderinglinefixcheckbox.CustomizableEdges = customizableEdges10;
            renderinglinefixcheckbox.Location = new Point(342, 340);
            renderinglinefixcheckbox.Name = "renderinglinefixcheckbox";
            renderinglinefixcheckbox.ShadowDecoration.CustomizableEdges = customizableEdges11;
            renderinglinefixcheckbox.Size = new Size(20, 20);
            renderinglinefixcheckbox.TabIndex = 103;
            renderinglinefixcheckbox.Text = "guna2CustomCheckBox2";
            renderinglinefixcheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            renderinglinefixcheckbox.UncheckedState.BorderRadius = 2;
            renderinglinefixcheckbox.UncheckedState.BorderThickness = 1;
            renderinglinefixcheckbox.UncheckedState.FillColor = Color.Transparent;
            renderinglinefixcheckbox.Click += renderinglinefixcheckbox_Click;
            // 
            // renderinglagfixcheckbox
            // 
            renderinglagfixcheckbox.Animated = true;
            renderinglagfixcheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            renderinglagfixcheckbox.CheckedState.BorderRadius = 2;
            renderinglagfixcheckbox.CheckedState.BorderThickness = 0;
            renderinglagfixcheckbox.CheckedState.FillColor = Color.Red;
            renderinglagfixcheckbox.CustomizableEdges = customizableEdges12;
            renderinglagfixcheckbox.Location = new Point(342, 364);
            renderinglagfixcheckbox.Name = "renderinglagfixcheckbox";
            renderinglagfixcheckbox.ShadowDecoration.CustomizableEdges = customizableEdges13;
            renderinglagfixcheckbox.Size = new Size(20, 20);
            renderinglagfixcheckbox.TabIndex = 102;
            renderinglagfixcheckbox.Text = "guna2CustomCheckBox3";
            renderinglagfixcheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            renderinglagfixcheckbox.UncheckedState.BorderRadius = 2;
            renderinglagfixcheckbox.UncheckedState.BorderThickness = 1;
            renderinglagfixcheckbox.UncheckedState.FillColor = Color.Transparent;
            renderinglagfixcheckbox.Click += renderinglagfixcheckbox_Click;
            // 
            // label28
            // 
            label28.AutoSize = true;
            label28.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label28.ForeColor = Color.DarkGray;
            label28.Location = new Point(207, 367);
            label28.Name = "label28";
            label28.Size = new Size(101, 13);
            label28.TabIndex = 101;
            label28.Text = "Rendering Lag Fix";
            // 
            // label29
            // 
            label29.AutoSize = true;
            label29.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label29.ForeColor = Color.DarkGray;
            label29.Location = new Point(207, 343);
            label29.Name = "label29";
            label29.Size = new Size(104, 13);
            label29.TabIndex = 100;
            label29.Text = "Rendering Line Fix";
            // 
            // label30
            // 
            label30.AutoSize = true;
            label30.Font = new Font("Segoe UI", 8F, FontStyle.Bold, GraphicsUnit.Point);
            label30.ForeColor = Color.DarkGray;
            label30.Location = new Point(297, 419);
            label30.Name = "label30";
            label30.Size = new Size(65, 13);
            label30.TabIndex = 104;
            label30.Text = "HX CHEATS";
            // 
            // pinontop
            // 
            pinontop.Animated = true;
            pinontop.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            pinontop.CheckedState.BorderRadius = 2;
            pinontop.CheckedState.BorderThickness = 0;
            pinontop.CheckedState.FillColor = Color.Red;
            pinontop.CustomizableEdges = customizableEdges8;
            pinontop.Location = new Point(342, 388);
            pinontop.Name = "pinontop";
            pinontop.ShadowDecoration.CustomizableEdges = customizableEdges9;
            pinontop.Size = new Size(20, 20);
            pinontop.TabIndex = 107;
            pinontop.Text = "guna2CustomCheckBox3";
            pinontop.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            pinontop.UncheckedState.BorderRadius = 2;
            pinontop.UncheckedState.BorderThickness = 1;
            pinontop.UncheckedState.FillColor = Color.Transparent;
            pinontop.Click += pinontop_Click;
            // 
            // label31
            // 
            label31.AutoSize = true;
            label31.Font = new Font("Segoe UI", 8.25F, FontStyle.Bold, GraphicsUnit.Point);
            label31.ForeColor = Color.DarkGray;
            label31.Location = new Point(207, 391);
            label31.Name = "label31";
            label31.Size = new Size(56, 13);
            label31.TabIndex = 106;
            label31.Text = "AIMKILL\\";
            // 
            // esplinecolorpicker
            // 
            esplinecolorpicker.BorderRadius = 2;
            esplinecolorpicker.CustomizableEdges = customizableEdges6;
            esplinecolorpicker.DisabledState.BorderColor = Color.DarkGray;
            esplinecolorpicker.DisabledState.CustomBorderColor = Color.DarkGray;
            esplinecolorpicker.DisabledState.FillColor = Color.FromArgb(169, 169, 169);
            esplinecolorpicker.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            esplinecolorpicker.FillColor = Color.White;
            esplinecolorpicker.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            esplinecolorpicker.ForeColor = Color.White;
            esplinecolorpicker.Location = new Point(316, 83);
            esplinecolorpicker.Name = "esplinecolorpicker";
            esplinecolorpicker.ShadowDecoration.CustomizableEdges = customizableEdges7;
            esplinecolorpicker.Size = new Size(20, 20);
            esplinecolorpicker.TabIndex = 108;
            esplinecolorpicker.Click += esplinecolorpicker_Click;
            // 
            // hookscheckbox
            // 
            hookscheckbox.Animated = true;
            hookscheckbox.CheckedState.BorderColor = Color.FromArgb(94, 148, 255);
            hookscheckbox.CheckedState.BorderRadius = 2;
            hookscheckbox.CheckedState.BorderThickness = 0;
            hookscheckbox.CheckedState.FillColor = Color.Red;
            hookscheckbox.CustomizableEdges = customizableEdges4;
            hookscheckbox.Location = new Point(152, 83);
            hookscheckbox.Name = "hookscheckbox";
            hookscheckbox.ShadowDecoration.CustomizableEdges = customizableEdges5;
            hookscheckbox.Size = new Size(20, 20);
            hookscheckbox.TabIndex = 109;
            hookscheckbox.Text = "guna2CustomCheckBox2";
            hookscheckbox.UncheckedState.BorderColor = Color.FromArgb(40, 40, 40);
            hookscheckbox.UncheckedState.BorderRadius = 2;
            hookscheckbox.UncheckedState.BorderThickness = 1;
            hookscheckbox.UncheckedState.FillColor = Color.Transparent;
            hookscheckbox.Click += hookscheckbox_Click;
            // 
            // guna2CircleButton1
            // 
            guna2CircleButton1.BackColor = Color.Transparent;
            guna2CircleButton1.BorderColor = Color.Red;
            guna2CircleButton1.DisabledState.BorderColor = Color.DarkGray;
            guna2CircleButton1.DisabledState.CustomBorderColor = Color.DarkGray;
            guna2CircleButton1.DisabledState.FillColor = Color.Red;
            guna2CircleButton1.DisabledState.ForeColor = Color.FromArgb(141, 141, 141);
            guna2CircleButton1.FillColor = Color.Blue;
            guna2CircleButton1.Font = new Font("Segoe UI", 9F, FontStyle.Regular, GraphicsUnit.Point);
            guna2CircleButton1.ForeColor = Color.Blue;
            guna2CircleButton1.Location = new Point(-28, -44);
            guna2CircleButton1.Name = "guna2CircleButton1";
            guna2CircleButton1.PressedColor = Color.Blue;
            guna2CircleButton1.PressedDepth = 0;
            guna2CircleButton1.ShadowDecoration.CustomizableEdges = customizableEdges3;
            guna2CircleButton1.ShadowDecoration.Mode = Guna.UI2.WinForms.Enums.ShadowMode.Circle;
            guna2CircleButton1.Size = new Size(62, 73);
            guna2CircleButton1.TabIndex = 110;
            // 
            // guna2ControlBox1
            // 
            guna2ControlBox1.Anchor = AnchorStyles.Top | AnchorStyles.Right;
            guna2ControlBox1.CustomizableEdges = customizableEdges1;
            guna2ControlBox1.FillColor = Color.Transparent;
            guna2ControlBox1.IconColor = Color.White;
            guna2ControlBox1.Location = new Point(339, -1);
            guna2ControlBox1.Name = "guna2ControlBox1";
            guna2ControlBox1.ShadowDecoration.CustomizableEdges = customizableEdges2;
            guna2ControlBox1.Size = new Size(45, 29);
            guna2ControlBox1.TabIndex = 111;
            guna2ControlBox1.Click += guna2ControlBox1_Click_2;
            // 
            // autorefresh
            // 
            autorefresh.Tick += autorefresh_Tick;
            // 
            // aimfovtrackvalue
            // 
            aimfovtrackvalue.BackColor = Color.Black;
            aimfovtrackvalue.FillColor = Color.FromArgb(20, 20, 20);
            aimfovtrackvalue.Location = new Point(17, 309);
            aimfovtrackvalue.Maximum = 500;
            aimfovtrackvalue.Name = "aimfovtrackvalue";
            aimfovtrackvalue.Size = new Size(154, 23);
            aimfovtrackvalue.Style = Guna.UI2.WinForms.Enums.TrackBarStyle.Metro;
            aimfovtrackvalue.TabIndex = 112;
            aimfovtrackvalue.ThumbColor = Color.Blue;
            aimfovtrackvalue.Value = 100;
            aimfovtrackvalue.Scroll += aimfovtrackvalue_Scroll;
            // 
            // smoothtrackvalue
            // 
            smoothtrackvalue.BackColor = Color.Black;
            smoothtrackvalue.FillColor = Color.FromArgb(20, 20, 20);
            smoothtrackvalue.Location = new Point(17, 345);
            smoothtrackvalue.Name = "smoothtrackvalue";
            smoothtrackvalue.Size = new Size(154, 23);
            smoothtrackvalue.Style = Guna.UI2.WinForms.Enums.TrackBarStyle.Metro;
            smoothtrackvalue.TabIndex = 113;
            smoothtrackvalue.ThumbColor = Color.Blue;
            smoothtrackvalue.Value = 0;
            smoothtrackvalue.Scroll += smoothtrackvalue_Scroll;
            // 
            // rangetrackvalue
            // 
            rangetrackvalue.BackColor = Color.Black;
            rangetrackvalue.FillColor = Color.FromArgb(20, 20, 20);
            rangetrackvalue.Location = new Point(17, 382);
            rangetrackvalue.Maximum = 150;
            rangetrackvalue.Name = "rangetrackvalue";
            rangetrackvalue.Size = new Size(154, 23);
            rangetrackvalue.Style = Guna.UI2.WinForms.Enums.TrackBarStyle.Metro;
            rangetrackvalue.TabIndex = 114;
            rangetrackvalue.ThumbColor = Color.Blue;
            rangetrackvalue.Value = 100;
            rangetrackvalue.Scroll += rangetrackvalue_Scroll;
            // 
            // Form1
            // 
            AutoScaleDimensions = new SizeF(7F, 15F);
            AutoScaleMode = AutoScaleMode.Font;
            BackColor = Color.Black;
            ClientSize = new Size(380, 439);
            Controls.Add(rangetrackvalue);
            Controls.Add(smoothtrackvalue);
            Controls.Add(aimfovtrackvalue);
            Controls.Add(guna2ControlBox1);
            Controls.Add(guna2CircleButton1);
            Controls.Add(hookscheckbox);
            Controls.Add(esplinecolorpicker);
            Controls.Add(pinontop);
            Controls.Add(label31);
            Controls.Add(label30);
            Controls.Add(renderinglinefixcheckbox);
            Controls.Add(renderinglagfixcheckbox);
            Controls.Add(label28);
            Controls.Add(label29);
            Controls.Add(streammodecheckbox);
            Controls.Add(autorefreshcheckbox);
            Controls.Add(label26);
            Controls.Add(label27);
            Controls.Add(aimbothexcheckbox);
            Controls.Add(guna2Separator11);
            Controls.Add(label25);
            Controls.Add(guna2Separator12);
            Controls.Add(guna2Separator10);
            Controls.Add(espskeletoncolorpicker);
            Controls.Add(espfillboxcolorpicker);
            Controls.Add(espboxcornercolorpicker);
            Controls.Add(espboxcolorpicker);
            Controls.Add(label24);
            Controls.Add(guna2ComboBox1);
            Controls.Add(espinfocheckbox);
            Controls.Add(label23);
            Controls.Add(espskeletoncheckbox);
            Controls.Add(label17);
            Controls.Add(espfillboxcheckbox);
            Controls.Add(label18);
            Controls.Add(espboxcornercheckbox);
            Controls.Add(label19);
            Controls.Add(espboxcheckbox);
            Controls.Add(label20);
            Controls.Add(esplinecheckbox);
            Controls.Add(label21);
            Controls.Add(guna2vSeparator3);
            Controls.Add(guna2Separator9);
            Controls.Add(guna2vSeparator4);
            Controls.Add(label22);
            Controls.Add(statuslabel);
            Controls.Add(guna2Separator8);
            Controls.Add(label15);
            Controls.Add(label16);
            Controls.Add(label13);
            Controls.Add(label14);
            Controls.Add(label12);
            Controls.Add(label11);
            Controls.Add(guna2Separator7);
            Controls.Add(drawcirclecheckbox);
            Controls.Add(label10);
            Controls.Add(guna2Separator6);
            Controls.Add(label9);
            Controls.Add(guna2Separator5);
            Controls.Add(ingoreknockcheckbox);
            Controls.Add(label8);
            Controls.Add(norecoilcheckbox);
            Controls.Add(label7);
            Controls.Add(aimbotlegitcheckbox);
            Controls.Add(label6);
            Controls.Add(aimbotragecheckbox);
            Controls.Add(label5);
            Controls.Add(label4);
            Controls.Add(guna2Separator4);
            Controls.Add(label3);
            Controls.Add(guna2vSeparator2);
            Controls.Add(guna2Separator3);
            Controls.Add(guna2vSeparator1);
            Controls.Add(label2);
            Controls.Add(guna2Separator2);
            Controls.Add(guna2Separator1);
            Controls.Add(label1);
            FormBorderStyle = FormBorderStyle.None;
            Name = "Form1";
            Text = "Mod Menu";
            Load += Form1_Load;
            ResumeLayout(false);
            PerformLayout();
        }

        #endregion

        private CheckBox checkESPBoxx;
        private CheckBox checkNoRecoill;
        private CheckBox checkEspNamesd;
        private CheckBox checkESPHealthh;
        private CheckBox checkIgnoreKnockedd;
        private Guna.UI2.WinForms.Guna2BorderlessForm guna2BorderlessForm1;
        private Label label2;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator2;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator1;
        private Label label1;
        private Label label3;
        private Guna.UI2.WinForms.Guna2VSeparator guna2vSeparator2;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator3;
        private Guna.UI2.WinForms.Guna2VSeparator guna2vSeparator1;
        private Guna.UI2.WinForms.Guna2CustomCheckBox aimbotlegitcheckbox;
        private Label label6;
        private Guna.UI2.WinForms.Guna2CustomCheckBox aimbotragecheckbox;
        private Label label5;
        private Label label4;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator4;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator6;
        private Label label9;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator5;
        private Guna.UI2.WinForms.Guna2CustomCheckBox ingoreknockcheckbox;
        private Label label8;
        private Guna.UI2.WinForms.Guna2CustomCheckBox norecoilcheckbox;
        private Label label7;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator7;
        private Guna.UI2.WinForms.Guna2CustomCheckBox drawcirclecheckbox;
        private Label label10;
        private Label label11;
        private Panel Slider2;
        private Label label13;
        private Label label14;
        private Label label12;
        private Label statuslabel;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator8;
        private Label label15;
        private Label label16;
        private Guna.UI2.WinForms.Guna2CustomCheckBox espskeletoncheckbox;
        private Label label17;
        private Guna.UI2.WinForms.Guna2CustomCheckBox espfillboxcheckbox;
        private Label label18;
        private Guna.UI2.WinForms.Guna2CustomCheckBox espboxcornercheckbox;
        private Label label19;
        private Guna.UI2.WinForms.Guna2CustomCheckBox espboxcheckbox;
        private Label label20;
        private Guna.UI2.WinForms.Guna2CustomCheckBox esplinecheckbox;
        private Label label21;
        private Guna.UI2.WinForms.Guna2VSeparator guna2vSeparator3;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator9;
        private Guna.UI2.WinForms.Guna2VSeparator guna2vSeparator4;
        private Label label22;
        private Guna.UI2.WinForms.Guna2CustomCheckBox espinfocheckbox;
        private Label label23;
        private Label label24;
        private Guna.UI2.WinForms.Guna2ComboBox guna2ComboBox1;
        private Guna.UI2.WinForms.Guna2Button espfillboxcolorpicker;
        private Guna.UI2.WinForms.Guna2Button espboxcornercolorpicker;
        private Guna.UI2.WinForms.Guna2Button espboxcolorpicker;
        private Guna.UI2.WinForms.Guna2Button espskeletoncolorpicker;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator10;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator11;
        private Label label25;
        private Guna.UI2.WinForms.Guna2Separator guna2Separator12;
        private Guna.UI2.WinForms.Guna2CustomCheckBox aimbothexcheckbox;
        private Guna.UI2.WinForms.Guna2CustomCheckBox streammodecheckbox;
        private Guna.UI2.WinForms.Guna2CustomCheckBox autorefreshcheckbox;
        private Label label26;
        private Label label27;
        private Guna.UI2.WinForms.Guna2CustomCheckBox renderinglinefixcheckbox;
        private Guna.UI2.WinForms.Guna2CustomCheckBox renderinglagfixcheckbox;
        private Label label28;
        private Label label29;
        private Label label30;
        private Guna.UI2.WinForms.Guna2CustomCheckBox pinontop;
        private Label label31;
        private Guna.UI2.WinForms.Guna2Button esplinecolorpicker;
        private Guna.UI2.WinForms.Guna2CustomCheckBox hookscheckbox;
        private Guna.UI2.WinForms.Guna2CircleButton guna2CircleButton1;
        private Guna.UI2.WinForms.Guna2ControlBox guna2ControlBox1;
        private System.Windows.Forms.Timer autorefresh;
        private Guna.UI2.WinForms.Guna2TrackBar rangetrackvalue;
        private Guna.UI2.WinForms.Guna2TrackBar smoothtrackvalue;
        private Guna.UI2.WinForms.Guna2TrackBar aimfovtrackvalue;
    }
}
