﻿using System.Numerics;

namespace AotForms
{
    internal static class Aimbot
    {
        private static CancellationTokenSource _cancellationTokenSource = new();
        private static bool _isRunning = false;
        private static readonly object _lockObject = new object();

        internal static async Task StartAsync()
        {
            lock (_lockObject)
            {
                if (_isRunning) return;
                _isRunning = true;
            }

            _cancellationTokenSource = new CancellationTokenSource();
            await Task.Run(() => WorkAsync(_cancellationTokenSource.Token), _cancellationTokenSource.Token);
        }

        internal static void Stop()
        {
            lock (_lockObject)
            {
                if (!_isRunning) return;
                _isRunning = false;
            }

            _cancellationTokenSource?.Cancel();
        }

        private static async Task WorkAsync(CancellationToken cancellationToken)
        {
            var targetFrameTime = PerformanceConfig.GetTargetFrameTime(PerformanceConfig.AimbotTargetFPS);
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                while (!cancellationToken.IsCancellationRequested)
                {
                    stopwatch.Restart();

                    try
                    {
                        if (!Config.AimBot)
                        {
                            await Task.Delay(PerformanceConfig.TaskDelayOnDisabled, cancellationToken);
                            continue;
                        }

                        if ((WinAPI.GetAsyncKeyState(Config.AimbotKey) & 0x8000) == 0)
                        {
                            await Task.Delay(PerformanceConfig.TaskDelayOnKeyNotPressed, cancellationToken);
                            continue;
                        }

                        await ProcessAimbotLogic(cancellationToken);
                    }
                    catch (OperationCanceledException)
                    {
                        break;
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue running
                        ErrorHandler.LogError("Aimbot processing error", ex);
                        await Task.Delay(PerformanceConfig.TaskDelayOnError, cancellationToken);
                    }

                    // Frame rate limiting
                    var elapsed = stopwatch.ElapsedMilliseconds;
                    if (elapsed < targetFrameTime)
                    {
                        await Task.Delay((int)(targetFrameTime - elapsed), cancellationToken);
                    }
                }
            }
            catch (OperationCanceledException)
            {
                // Expected when cancellation is requested
            }
            finally
            {
                lock (_lockObject)
                {
                    _isRunning = false;
                }
            }
        }

        private static async Task ProcessAimbotLogic(CancellationToken cancellationToken)
        {
            Entity target = null;
            float distance = float.MaxValue;

            // Validate core data
            if (Core.Width == -1 || Core.Height == -1 || !Core.HaveMatrix)
                return;

            var screenCenter = new Vector2(Core.Width / 2f, Core.Height / 2f);

            // Create a snapshot of entities to avoid collection modification issues
            var entitiesSnapshot = Core.Entities.Values.ToArray();

            foreach (var entity in entitiesSnapshot)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (entity?.IsDead == true || entity?.IsKnown != true) continue;

                try
                {
                    var head2D = W2S.WorldToScreen(Core.CameraMatrix, entity.Head, Core.Width, Core.Height);
                    var root2D = W2S.WorldToScreen(Core.CameraMatrix, entity.Root, Core.Width, Core.Height);

                    if (head2D.X < 1 || head2D.Y < 1 || root2D.X < 1 || root2D.Y < 1) continue;

                    var playerDistance = Vector3.Distance(Core.LocalMainCamera, entity.Head);
                    if (playerDistance > Config.AimBotMaxDistance) continue;

                    var x = head2D.X - screenCenter.X;
                    var y = head2D.Y - screenCenter.Y;
                    var crosshairDist = MathF.Sqrt(x * x + y * y);

                    if (crosshairDist >= distance || crosshairDist > Config.Aimfov) continue;

                    distance = crosshairDist;
                    target = entity;
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError("Entity processing error in aimbot", ex);
                    continue;
                }
            }

            if (target != null)
            {
                try
                {
                    var playerLook = MathUtils.GetRotationToLocation(target.Head, 0.1f, Core.LocalMainCamera);
                    InternalMemory.Write(Core.LocalPlayer + Offsets.AimRotation, playerLook);
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError("Aimbot write error", ex);
                }
            }
        }

        // Legacy method for backward compatibility
        internal static void Work()
        {
            _ = StartAsync();
        }
    }
}