{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\INTERNAL SAFE\\AotForms\\AotForms.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\INTERNAL SAFE\\AotForms\\AotForms.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\INTERNAL SAFE\\AotForms\\AotForms.csproj", "projectName": "AotForms", "projectPath": "C:\\Users\\<USER>\\Desktop\\INTERNAL SAFE\\AotForms\\AotForms.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\INTERNAL SAFE\\AotForms\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net7.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net7.0-windows7.0": {"targetAlias": "net7.0-windows", "dependencies": {"ClickableTransparentOverlay": {"target": "Package", "version": "[9.3.0, )"}, "DNNE": {"target": "Package", "version": "[2.0.6, )"}, "Guna.UI2.WinForms": {"target": "Package", "version": "[2.0.4.6, )"}, "Microsoft.DotNet.ILCompiler": {"suppressParent": "All", "target": "Package", "version": "[7.0.20, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Analyzers": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[7.0.100-1.23211.1, )", "autoReferenced": true}, "WinFormsComInterop": {"target": "Package", "version": "[0.5.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.NETCore.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "Microsoft.WindowsDesktop.App.Ref", "version": "[7.0.20, 7.0.20]"}, {"name": "runtime.win-x64.Microsoft.DotNet.ILCompiler", "version": "[7.0.20, 7.0.20]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WindowsForms": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}