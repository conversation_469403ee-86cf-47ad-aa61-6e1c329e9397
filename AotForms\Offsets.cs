﻿

namespace AotForms {
    internal static class Offsets {
        internal static uint Il2Cpp;
        internal static uint InitBase = 0x8CA88B4;
        internal static uint StaticClass = 0x5c;

        internal static uint CurrentMatch = 0x50;
        internal static uint MatchStatus = 0x3c;
        internal static uint LocalPlayer = 0x44;
        internal static uint DictionaryEntities = 0x68;

        internal static uint Player_IsDead = 0x4c;
        internal static uint Player_Name = 0x224;
        internal static uint Player_Data = 0x44;
        internal static uint Player_ShadowBase = 0x11d0;
        internal static uint XPose = 0x78;

        internal static uint AvatarManager = 0x3f4;
        internal static uint Avatar = 0x94;
        internal static uint Avatar_IsVisible = 0x7c;
        internal static uint Avatar_Data = 0x10;
        internal static uint Avatar_Data_IsTeam = 0x51;

        internal static uint FollowCamera = 0x384;
        internal static uint Camera = 0x14;
        internal static uint AimRotation = 0x33c;
        internal static uint MainCameraTransform = 0x194;

        internal static uint Weapon = 0x330;
        internal static uint WeaponData = 0x44;
        internal static uint WeaponRecoil = 0xc;
        internal static uint ViewMatrix = 0xBC;// 0x98 + 0x24; = 0xBC
        internal static uint GetKillRequest = 0x9C;
        internal static uint Aimkill = 0x9C;
        internal static uint PlayerData_Aimkill = 0x9C;

    }
}
