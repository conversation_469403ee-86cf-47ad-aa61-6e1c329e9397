using System;
using System.Collections.Concurrent;
using System.Runtime;
using System.Runtime.CompilerServices;
using System.Threading;
using System.Threading.Tasks;

namespace AotForms
{
    /// <summary>
    /// Advanced performance optimizations and system tuning
    /// </summary>
    internal static class AdvancedOptimizations
    {
        private static Timer _systemMonitorTimer;
        private static volatile bool _isOptimized = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// Apply all advanced optimizations
        /// </summary>
        public static void ApplyAllOptimizations()
        {
            lock (_lockObject)
            {
                if (_isOptimized) return;

                try
                {
                    OptimizeGarbageCollector();
                    OptimizeThreadPool();
                    OptimizeJIT();
                    StartSystemMonitoring();
                    
                    _isOptimized = true;
                    ErrorHandler.LogInfo("Advanced optimizations applied successfully");
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogError("Failed to apply advanced optimizations", ex);
                }
            }
        }

        /// <summary>
        /// Optimize garbage collector settings
        /// </summary>
        private static void OptimizeGarbageCollector()
        {
            try
            {
                // Set latency mode for better performance
                GCSettings.LatencyMode = GCLatencyMode.SustainedLowLatency;
                
                // Configure server GC if available
                if (GCSettings.IsServerGC)
                {
                    ErrorHandler.LogInfo("Server GC is enabled - optimal for performance");
                }
                else
                {
                    ErrorHandler.LogInfo("Workstation GC is enabled - consider server GC for better performance");
                }

                ErrorHandler.LogInfo($"GC Latency Mode set to: {GCSettings.LatencyMode}");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Failed to optimize garbage collector", ex);
            }
        }

        /// <summary>
        /// Optimize thread pool settings
        /// </summary>
        private static void OptimizeThreadPool()
        {
            try
            {
                var processorCount = Environment.ProcessorCount;
                
                // Set optimal thread pool sizes
                var minWorkerThreads = Math.Max(processorCount, 4);
                var maxWorkerThreads = Math.Max(processorCount * 4, 16);
                var minCompletionPortThreads = Math.Max(processorCount, 4);
                var maxCompletionPortThreads = Math.Max(processorCount * 2, 8);

                ThreadPool.SetMinThreads(minWorkerThreads, minCompletionPortThreads);
                ThreadPool.SetMaxThreads(maxWorkerThreads, maxCompletionPortThreads);

                ErrorHandler.LogInfo($"ThreadPool optimized: Workers({minWorkerThreads}-{maxWorkerThreads}), " +
                    $"IO({minCompletionPortThreads}-{maxCompletionPortThreads})");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Failed to optimize thread pool", ex);
            }
        }

        /// <summary>
        /// Optimize JIT compiler settings
        /// </summary>
        private static void OptimizeJIT()
        {
            try
            {
                // Enable tiered compilation for better performance
                AppContext.SetSwitch("System.Runtime.TieredCompilation", true);
                AppContext.SetSwitch("System.Runtime.TieredCompilation.QuickJit", true);
                
                ErrorHandler.LogInfo("JIT optimizations enabled (Tiered Compilation)");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Failed to optimize JIT", ex);
            }
        }

        /// <summary>
        /// Start continuous system monitoring
        /// </summary>
        private static void StartSystemMonitoring()
        {
            try
            {
                _systemMonitorTimer = new Timer(MonitorSystemHealth, null, 
                    TimeSpan.FromMinutes(1), TimeSpan.FromMinutes(1));
                
                ErrorHandler.LogInfo("System monitoring started");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Failed to start system monitoring", ex);
            }
        }

        /// <summary>
        /// Monitor system health and performance
        /// </summary>
        private static void MonitorSystemHealth(object state)
        {
            try
            {
                var totalMemory = GC.GetTotalMemory(false);
                var gen0Collections = GC.CollectionCount(0);
                var gen1Collections = GC.CollectionCount(1);
                var gen2Collections = GC.CollectionCount(2);

                // Check for memory pressure
                if (totalMemory > PerformanceConfig.MemoryPressureThresholdMB * 1024 * 1024)
                {
                    ErrorHandler.LogWarning($"High memory usage detected: {totalMemory / 1024 / 1024}MB");
                    
                    // Trigger aggressive cleanup
                    _ = Task.Run(() =>
                    {
                        InternalMemory.ClearCache();
                        GC.Collect(2, GCCollectionMode.Aggressive);
                        GC.WaitForPendingFinalizers();
                        GC.Collect(2, GCCollectionMode.Aggressive);
                    });
                }

                // Check for excessive GC activity
                var totalCollections = gen0Collections + gen1Collections + gen2Collections;
                if (totalCollections > 1000) // Arbitrary threshold
                {
                    ErrorHandler.LogWarning($"High GC activity: Gen0={gen0Collections}, Gen1={gen1Collections}, Gen2={gen2Collections}");
                }

                // Log periodic health status
                ErrorHandler.LogInfo($"System Health: Memory={totalMemory / 1024 / 1024}MB, " +
                    $"GC Collections={totalCollections}, Cache Size={InternalMemory.GetCacheSize()}");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("System monitoring error", ex);
            }
        }

        /// <summary>
        /// Cleanup and dispose resources
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                _systemMonitorTimer?.Dispose();
                _systemMonitorTimer = null;
                _isOptimized = false;
                
                ErrorHandler.LogInfo("Advanced optimizations cleaned up");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Cleanup error", ex);
            }
        }
    }

    /// <summary>
    /// CPU-specific optimizations
    /// </summary>
    internal static class CPUOptimizations
    {
        /// <summary>
        /// Apply CPU-specific optimizations
        /// </summary>
        public static void OptimizeForCPU()
        {
            try
            {
                var processorCount = Environment.ProcessorCount;
                var is64Bit = Environment.Is64BitProcess;
                
                ErrorHandler.LogInfo($"CPU Info: {processorCount} cores, {(is64Bit ? "64-bit" : "32-bit")} process");

                // Set process priority
                try
                {
                    using var currentProcess = System.Diagnostics.Process.GetCurrentProcess();
                    currentProcess.PriorityClass = System.Diagnostics.ProcessPriorityClass.High;
                    ErrorHandler.LogInfo("Process priority set to High");
                }
                catch (Exception ex)
                {
                    ErrorHandler.LogWarning($"Could not set process priority: {ex.Message}");
                }

                // Optimize for specific CPU configurations
                if (processorCount >= 8)
                {
                    // High-end CPU optimizations
                    PerformanceConfig.AimbotTargetFPS = 120;
                    PerformanceConfig.DataCollectionTargetFPS = 60;
                    PerformanceConfig.MaxEntitiesPerFrame = 100;
                }
                else if (processorCount >= 4)
                {
                    // Mid-range CPU optimizations
                    PerformanceConfig.AimbotTargetFPS = 60;
                    PerformanceConfig.DataCollectionTargetFPS = 30;
                    PerformanceConfig.MaxEntitiesPerFrame = 50;
                }
                else
                {
                    // Low-end CPU optimizations
                    PerformanceConfig.AimbotTargetFPS = 30;
                    PerformanceConfig.DataCollectionTargetFPS = 20;
                    PerformanceConfig.MaxEntitiesPerFrame = 25;
                }

                ErrorHandler.LogInfo("CPU-specific optimizations applied");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("CPU optimization error", ex);
            }
        }
    }

    /// <summary>
    /// Memory-specific optimizations
    /// </summary>
    internal static class MemoryOptimizations
    {
        private static Timer _memoryCleanupTimer;

        /// <summary>
        /// Start advanced memory management
        /// </summary>
        public static void StartAdvancedMemoryManagement()
        {
            try
            {
                // Start periodic memory cleanup
                _memoryCleanupTimer = new Timer(PerformMemoryCleanup, null,
                    TimeSpan.FromMinutes(PerformanceConfig.GCIntervalMinutes),
                    TimeSpan.FromMinutes(PerformanceConfig.GCIntervalMinutes));

                ErrorHandler.LogInfo("Advanced memory management started");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Failed to start memory management", ex);
            }
        }

        /// <summary>
        /// Perform intelligent memory cleanup
        /// </summary>
        private static void PerformMemoryCleanup(object state)
        {
            try
            {
                var beforeMemory = GC.GetTotalMemory(false);
                
                // Clear caches if they're too large
                var cacheSize = InternalMemory.GetCacheSize();
                if (cacheSize > PerformanceConfig.MaxCacheSize * 0.8) // 80% threshold
                {
                    InternalMemory.ClearCache();
                    ErrorHandler.LogInfo($"Cache cleared: {cacheSize} entries removed");
                }

                // Clear entity cache if too many entities
                if (Core.Entities?.Count > PerformanceConfig.MaxEntitiesPerFrame * 2)
                {
                    var oldCount = Core.Entities.Count;
                    Core.Entities.Clear();
                    ErrorHandler.LogInfo($"Entity cache cleared: {oldCount} entities removed");
                }

                // Perform garbage collection if memory usage is high
                if (beforeMemory > PerformanceConfig.MemoryPressureThresholdMB * 1024 * 1024)
                {
                    GC.Collect(1, GCCollectionMode.Optimized);
                    GC.WaitForPendingFinalizers();
                    
                    var afterMemory = GC.GetTotalMemory(true);
                    var freedMemory = beforeMemory - afterMemory;
                    
                    ErrorHandler.LogInfo($"Memory cleanup: {freedMemory / 1024 / 1024}MB freed " +
                        $"({beforeMemory / 1024 / 1024}MB -> {afterMemory / 1024 / 1024}MB)");
                }
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Memory cleanup error", ex);
            }
        }

        /// <summary>
        /// Stop memory management
        /// </summary>
        public static void Stop()
        {
            try
            {
                _memoryCleanupTimer?.Dispose();
                _memoryCleanupTimer = null;
                
                ErrorHandler.LogInfo("Advanced memory management stopped");
            }
            catch (Exception ex)
            {
                ErrorHandler.LogError("Memory management stop error", ex);
            }
        }
    }
}
